# Stage 1: Build the Vite app
FROM node:18-alpine AS build


# Set build arguments
ARG BASE_PATH

# Set environment variables
ENV BASE_PATH=$BASE_PATH

# Set working directory
WORKDIR /app

# Copy package files and install dependencies
COPY package.json package-lock.json .env ./
RUN npm install --legacy-peer-deps

# Copy the rest of the application files
COPY . .

# Build the application for production
RUN npm run build:serve

# Stage 2: Serve the app with Nginx
FROM nginx:alpine

# Remove default Nginx static assets
RUN rm -rf /usr/share/nginx/html/*

# Copy built assets from the previous stage
COPY --from=build /app/dist /usr/share/nginx/html
COPY --from=build /app/dist /usr/share/nginx/html/thue-xe-san-bay

# Copy custom Nginx configuration (optional)
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port 8080
EXPOSE 8080

# Start Nginx
CMD ["nginx", "-g", "daemon off;"]