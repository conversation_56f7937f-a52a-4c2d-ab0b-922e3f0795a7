import axios from "axios";

export interface Holiday {
  id: number;
  holidayDate: string;
  name: string;
  amount: number;
  type: "FIXED" | "PERCENTAGE";
  currency?: "USD" | "VND";
}

const API_BASE_URL = import.meta.env.VITE_BASE_API_ENDPOINT;

const axiosInstance = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
});

class HolidayService {
  private static instance: HolidayService;

  private constructor() {}

  public static getInstance(): HolidayService {
    if (!HolidayService.instance) {
      HolidayService.instance = new HolidayService();
    }
    return HolidayService.instance;
  }

  async getHolidayPricing(): Promise<Holiday[]> {
    try {
      const { data } = await axiosInstance.get<Holiday[]>(
        "/booking/holiday-pricing"
      );
      return data.map((holiday) => ({ ...holiday, currency: "VND" }));
    } catch (error) {
      console.error("Failed to fetch holiday pricing:", error);
      return [];
    }
  }

  async getHolidayPricingUSD(): Promise<Holiday[]> {
    try {
      const { data } = await axiosInstance.get<Holiday[]>(
        "/booking/holiday-pricing?currency=USD"
      );
      return data;
    } catch (error) {
      console.error("Failed to fetch holiday pricing:", error);
      return [];
    }
  }
}

export const holidayService = HolidayService.getInstance();
