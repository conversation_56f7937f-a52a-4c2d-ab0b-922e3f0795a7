import axios from "axios";
// Config cho API
const API_BASE_URL = import.meta.env.VITE_BASE_API_ENDPOINT;

// Tạo instance axios với cấu hình mặc định
const axiosInstance = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
});

export interface ApiResponse<T = unknown> {
  data?: T;
  error?: string;
  message?: string;
  status: number;
}

axiosInstance.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add interceptor to handle response
axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error("API request failed:", error);
    return Promise.reject(error);
  }
);

export interface LocationResponse {
  id: number;
  name: string;
  nameEn: string;
  code: string;
  provinceCode: string;
  countryCode: string;
  isAirport: boolean;
  isPopular: boolean;
  movingTime: string;
  prices:
    | {
        vehicleType: string;
        vnd: number;
        usd: number;
      }[]
    | null;
}

export interface AirportLocationResponse {
  id: number;
  code: string;
  name: string;
  address: string | null;
  district: string | null;
  province: string | null;
  country: string | null;
  latitude: string | null;
  longitude: string | null;
  dealerId: string | null;
  status: string | null;
}

export interface AutocompletePrediction {
  placeId: string;
  address: string;
  name: string;
  latitude: string;
  longitude: string;
  code: string;
}

export interface AirportPricingResponse {
  price: number; // Giá tiền (VND)
  priceUSD?: number; // Giá tiền (USD)
  distance: string; // Quãng đường (km dạng string)
  duration: string; // Thời gian di chuyển (phút dạng string)
  model: string; // Tên model xe (VD: VF8, VF9, VF10)
  roundedMinutes: number; // Số phút làm tròn (block thời gian tính giá)
  priceBlock: string; // Tên block thời gian (VD: "1 giờ")
  sku?: string; // SKU của sản phẩm (VD: "AIRPORT_VF8_2H")
  quoteToken: string;
}

// Interface cho location trong payload
export interface LocationPayload {
  address: string;
  latitude: string;
  longitude: string;
  placeId: string;
}

// Interface cho payload của API calculateAirportBooking
export interface CalculateAirportBookingPayload {
  origin: LocationPayload;
  destination: LocationPayload | { latitude: string; longitude: string };
  model: string[];
  pickupDate: string;
  durationInMinutes?: number;
}

/**
 * Get location from API
 */
export const getLocation = async (): Promise<
  ApiResponse<LocationResponse[]>
> => {
  try {
    const response = await axiosInstance.get("/locations");
    return {
      data: response.data,
      status: response.status,
    };
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : "Unknown error",
      status: 500,
    };
  }
};

/**
 * Get airport locations from API
 */
export const getAirportLocations = async (): Promise<
  ApiResponse<{ content: AirportLocationResponse[] }>
> => {
  try {
    const response = await axiosInstance.get("/booking/location", {
      params: {
        type: "airport",
      },
    });
    return {
      data: response.data,
      status: response.status,
    };
  } catch (error) {
    console.error("Error fetching airport locations:", error);
    return {
      error: error instanceof Error ? error.message : "Unknown error",
      status: 500,
    };
  }
};

/**
 * Get location suggestions from Google Maps Autocomplete API
 * @param input Search text input
 * @param language Language code (vi or en)
 */
export const getLocationSuggestions = async (
  input: string,
  language: string = 'vi'
): Promise<ApiResponse<AutocompletePrediction[]>> => {
  if (!input || input.length < 2) {
    return {
      data: [],
      error: "Input must be at least 2 characters long",
      status: 200,
    };
  }

  try {
    const response = await axiosInstance.get("/booking/maps/autocomplete", {
      params: {
        input: input,
        language
      },
    });
    return {
      data: response.data,
      status: response.status,
    };
  } catch (error) {
    console.error("Error fetching location suggestions:", error);
    return {
      error: error instanceof Error ? error.message : "Unknown error",
      status: 500,
    };
  }
};
export const calculateAirportBooking = async (
  payload: CalculateAirportBookingPayload,
  captchaToken?: string
): Promise<ApiResponse<AirportPricingResponse[]>> => {
  if (!payload) {
    return {
      data: [],
      error: "Payload is required",
      status: 400,
    };
  }

  try {
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    // Thêm token reCAPTCHA vào header nếu có
    if (captchaToken) {
      headers["X-Recaptcha-Token"] = captchaToken;
    }

    const response = await axiosInstance.post(
      "/booking/airport/calculate",
      payload,
      { headers }
    );

    return {
      data: response.data,
      status: response.status,
    };
  } catch (error) {
    console.error("Error calculating airport booking:", error);

    // Kiểm tra nếu là lỗi Axios và có response
    if (axios.isAxiosError(error) && error.response) {
      // Lấy thông tin lỗi từ response
      const errorCode = error.response.data?.code || '';
      const errorMessage = error.response.data?.message || '';

      return {
        error: `${errorCode}: ${errorMessage}`,
        status: error.response.status,
      };
    }

    return {
      error: error instanceof Error ? error.message : "Unknown error",
      status: 500,
    };
  }
};
