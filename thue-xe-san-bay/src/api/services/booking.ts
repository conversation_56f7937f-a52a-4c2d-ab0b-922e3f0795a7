import axios from "axios";
import { useLocationStore } from "@/stores/useLocationStore";
import { useSearchStore } from "@/stores/useSearchStore";

// Config cho API
const API_BASE_URL = import.meta.env.VITE_BASE_API_ENDPOINT;

// Tạo instance axios với cấu hình mặc định
const axiosInstance = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
});

// Thông tin đầu vào cho request đặt xe
export interface BookingRequestInput {
  campaign_code?: string;
  utm_campaign?: string;
  utm_source?: string;
  utm_medium?: string;
  utm_term?: string;
  utm_id?: string;

  destination_code: string; // Điểm đi
  pickup_code: string; // Điểm đến
  pickup_date_time: string; // Ngày giờ đi
  vehicle_type: string; // Loại xe được chọn
  sku: string; // SKU của sản phẩm từ API calculate
  quoteToken?: string;
  rentalType?: "ROUTE" | "HOURLY"; // Loại thuê xe: ROUTE (theo hành trình) hoặc HOURLY (theo giờ)
  locationId?: number; // ID của location từ API getAirportLocations

  // Không cần place_id nữa vì đã sử dụng address trong API

  phone_number?: string;
  email?: string;
  full_name: string;

  notes?: string; // Ghi chú
  flight_code?: string; // Mã chuyến bay
}

// Định nghĩa type cho request đặt xe gửi lên API
export interface BookingRequest {
  fullName: string;
  phoneNumber?: string;
  email?: string;
  notes?: string;
  pickupDateTime: string;
  flightNumber?: string;
  rentalType?: "ROUTE" | "HOURLY"; // Loại thuê xe: ROUTE (theo hành trình) hoặc HOURLY (theo giờ)
  origin?: string; // Địa chỉ điểm đón
  destination?: string; // Địa chỉ điểm đến
  locationId?: number; // ID của location từ API getAirportLocations
  quoteToken: string;
  details: {
    items: [
      {
        sku: string; // SKU của sản phẩm, ví dụ: "AIRPORT_VF8_1H"
        quantity: number; // Mặc định là 1
      }
    ]
  };
  // Thêm các tham số UTM
  campaignCode?: string;
  utmCampaign?: string;
  utmSource?: string;
  utmMedium?: string;
  utmTerm?: string;
  utmId?: string;
}

// Type cho response từ server
export interface ApiResponse<T = unknown> {
  data?: T;
  error?: string;
  message?: string;
  status: number;
}

// Thêm interceptor để xử lý request
axiosInstance.interceptors.request.use(
  (config) => {
    // Thêm xử lý CORS và headers khác nếu cần
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add interceptor to handle response
axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error("API request failed:", error);
    return Promise.reject(error);
  }
);

/**
 * Send booking request to API
 * @param params Thông tin đặt xe
 * @param captchaToken Token captcha để xác thực (sẽ được gửi trong header)
 */
export const postBooking = async (
  params: BookingRequestInput,
  captchaToken: string
): Promise<ApiResponse> => {
  try {
    // Sử dụng SKU từ API calculate nếu có, nếu không thì tạo SKU từ vehicle_type
    const sku = params.sku || `AIRPORT_${params.vehicle_type}_1H`;

    // Lấy thông tin địa chỉ từ useLocationStore và useSearchStore
    const { origin, destination } = useLocationStore.getState();
    const { pickUpLocation } = useSearchStore.getState();

    // Get locationId from params, pickUpLocation, origin, or destination
    // For journey bookings, the ID could be in origin or destination
    // For hourly bookings, the ID would be in pickUpLocation
    const locationId = params.locationId ||
                       pickUpLocation?.id ||
                       (origin?.id && origin.code ? origin.id : undefined) ||
                       (destination?.id && destination.code ? destination.id : undefined);

    const transformedParams: BookingRequest = {
      fullName: params.full_name,
      phoneNumber: params.phone_number,
      email: params.email,
      notes: params.notes,
      pickupDateTime: params.pickup_date_time,
      flightNumber: params.flight_code,
      rentalType: params.rentalType, // Thêm rentalType vào request
      origin: origin?.address || origin?.name || "", // Ưu tiên sử dụng address thay vì name
      destination: destination?.address || destination?.name || "", // Ưu tiên sử dụng address thay vì name
      locationId: locationId, // Use the locationId from any of the available sources
      quoteToken: params.quoteToken || "", 
      details: {
        items: [
          {
            sku: sku,
            quantity: 1
          }
        ]
      },
      // Chuyển từ snake_case sang camelCase khi gửi sang BE
      campaignCode: params.campaign_code,
      utmCampaign: params.utm_campaign,
      utmSource: params.utm_source,
      utmMedium: params.utm_medium,
      utmTerm: params.utm_term,
      utmId: params.utm_id
    };

    // Gọi API với cấu trúc mới
    const response = await axiosInstance.post("/booking/client/airport", transformedParams, {
      headers: {
        "X-Recaptcha-Token": captchaToken
      }
    });

    return {
      data: response.data,
      status: response.status,
      message: response.statusText,
    };
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return {
        error: error.response?.data?.message || error.message,
        status: error.response?.status || 500,
        message: error.response?.statusText || "Server error",
      };
    }

    return {
      error: "Unknown error",
      status: 500,
      message: "Server error",
    };
  }
};
