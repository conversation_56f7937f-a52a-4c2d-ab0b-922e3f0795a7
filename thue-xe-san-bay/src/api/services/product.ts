import axios from "axios";

// Config cho API
const API_BASE_URL = import.meta.env.VITE_BASE_API_ENDPOINT;

// Tạo instance axios với cấu hình mặc định
const axiosInstance = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
});

// Type cho response từ server
export interface ApiResponse<T = unknown> {
  data?: T;
  error?: string;
  message?: string;
  status: number;
}

// Add interceptor to handle response
axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error("API request failed:", error);
    return Promise.reject(error);
  }
);

/**
 * Get product variants from API
 */
export const getProductVariants = async (serviceType: string): Promise<ApiResponse> => {
  try {
    const response = await axiosInstance.get("/booking/product-variant", {
      params: {
        serviceType
      }
    });

    return {
      data: response.data,
      status: response.status,
    };
  } catch (error) {
    console.error("Error fetching product variants:", error);
    return {
      error: error instanceof Error ? error.message : "Unknown error",
      status: 500,
    };
  }
};
