import { useMemo } from 'react';
import queryString from 'query-string';

/**
 * Hook để lấy các tham số UTM từ URL
 */
export const useUtmParams = () => {
  const utmParams = useMemo(() => {
    const parsed = queryString.parse(window.location.search);
    
    return {
      campaign_code: parsed.campaign_code as string | undefined,
      utm_campaign: parsed.utm_campaign as string | undefined,
      utm_source: parsed.utm_source as string | undefined,
      utm_medium: parsed.utm_medium as string | undefined,
      utm_term: parsed.utm_term as string | undefined,
      utm_id: parsed.utm_id as string | undefined,
    };
  }, []);

  return utmParams;
};

export default useUtmParams; 