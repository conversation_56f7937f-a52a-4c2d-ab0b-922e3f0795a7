import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "../ui/button";
import { SwapIcon } from "../../assets/icons";
import { useTranslation } from "react-i18next";
import DateTimePicker from "../search/DateTimePicker";
import { searchFormSchema, type SearchFormValues } from "../../lib/validations";
import { useBookingModeStore } from "@/stores/useBookingModeStore";
import LocationInput, { LocationItem } from "../search/LocationInput";
import { useLocationStore } from "@/stores/useLocationStore";
import { useSearchStore } from "@/stores";

interface BookingFormByRouteProps {
  onSubmit: (data: SearchFormValues) => void;
  initialValues?: Partial<SearchFormValues>;
}

const BookingFormByRoute: React.FC<BookingFormByRouteProps> = ({
  onSubmit,
  initialValues,
}) => {
  const { t } = useTranslation();
  const {
    pickup,
    destination,
    date,
    time,
    swapLocations,
    setPickup,
    setDestination,
    setDate,
    setTime,
  } = useSearchStore();
  const { setMode } = useBookingModeStore();

  // Set mode to journey when component mounts
  useEffect(() => {
    setMode("journey");
  }, [setMode]);
  const { setOrigin, setDestination: setLocationStoreDestination } =
    useLocationStore();
  const [pickupSelected, setPickupSelected] = useState(!!pickup);
  const [destinationSelected, setDestinationSelected] = useState(!!destination);

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    setError,
    clearErrors,
  } = useForm<SearchFormValues>({
    resolver: zodResolver(searchFormSchema),
    defaultValues: {
      pickup: pickup || initialValues?.pickup || "",
      destination: destination || initialValues?.destination || "",
      date: date || initialValues?.date || new Date(),
      time: time || initialValues?.time || "",
    },
    mode: "onChange",
  });

  const handleFormSubmit = () => {
    // Validate that locations are selected from dropdown
    if (!pickupSelected) {
      setError("pickup", {
        type: "manual",
        message: t(
          "validation.selectFromDropdown",
          "Please select a location from the dropdown"
        ),
      });
      return;
    }

    if (!destinationSelected) {
      setError("destination", {
        type: "manual",
        message: t(
          "validation.selectFromDropdown",
          "Please select a location from the dropdown"
        ),
      });
      return;
    }

    handleSubmit((data) => {
      setDate(data.date);
      setTime(data.time);
      onSubmit(data);
    })();
  };

  return (
    <div className="bg-white rounded-[20px] shadow-lg p-1">
      {/* Desktop Layout - Hidden on mobile, visible from lg up */}
      <div className="hidden lg:flex flex-nowrap gap-1">
        <div className="flex-1 p-2">
          <label className="block text-sm font-medium text-gray-700 py-1 mb-1">
            {t("searchForm.pickup", "Pickup location")}{" "}
            <span className="text-red-500">*</span>
          </label>
          <LocationInput
            value={pickup}
            onChange={(value, location?: LocationItem) => {
              setValue("pickup", value);
              if (location) {
                // Cập nhật pickup trong useSearchStore
                setPickup(location);

                // Cập nhật origin trong useLocationStore
                setOrigin(location);
                setPickupSelected(true);
                clearErrors("pickup");
              } else {
                setPickupSelected(false);
              }
            }}
            placeholder={t("searchForm.pickup", "Pickup location")}
            error={errors.pickup?.message}
            className="h-12"
            isPickup={true}
            isOrigin={true}
          />
        </div>

        <div
          className={`flex items-center justify-center mt-8 ${
            errors.pickup ? "mb-5" : ""
          }`}
        >
          <button
            onClick={(e) => {
              e.preventDefault();
              swapLocations();
              // Update selection state when swapping
              const tempPickupSelected = pickupSelected;
              setPickupSelected(destinationSelected);
              setDestinationSelected(tempPickupSelected);
            }}
            className="bg-gray-100 hover:bg-gray-200 p-2 rounded-full transition-colors duration-200"
            aria-label={t(
              "searchForm.swapLocations",
              "Swap pickup and destination"
            )}
          >
            <SwapIcon />
          </button>
        </div>

        <div className="flex-1 p-2">
          <label className="block text-sm font-medium text-gray-700 py-1 mb-1">
            {t("searchForm.destination", "Destination")}{" "}
            <span className="text-red-500">*</span>
          </label>
          <LocationInput
            value={destination}
            onChange={(value, location?: LocationItem) => {
              setValue("destination", value);
              if (location) {
                // Cập nhật destination trong useSearchStore
                setDestination(location);

                // Cập nhật destination trong useLocationStore
                setLocationStoreDestination(location);
                setDestinationSelected(true);
                clearErrors("destination");
              } else {
                setDestinationSelected(false);
              }
            }}
            placeholder={t("searchForm.destination", "Destination")}
            error={errors.destination?.message}
            className="h-12"
            isPickup={false}
          />
        </div>

        <DateTimePicker
          dateControl={control}
          timeControl={control}
          errors={errors}
        />

        <div
          className={`lg:w-1/4 p-2 flex items-end ${
            Object.keys(errors).length > 0 ? "mb-6" : ""
          }`}
        >
          <div className="w-full mb-1">
            <Button
              className="w-full h-12 bg-[#07F668] hover:bg-[#00C853] text-stone-950 text-md rounded-full md:font-medium cursor-pointer"
              onClick={handleFormSubmit}
            >
              {t("searchForm.search", "Search")}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Layout - Visible on mobile, hidden from lg up */}
      <div className="flex lg:hidden flex-col gap-2 p-2">
        <div className="flex items-center gap-2 relative">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 py-1 mb-1">
              {t("searchForm.pickup", "Pickup location")}{" "}
              <span className="text-red-500">*</span>
            </label>
            <LocationInput
              value={pickup}
              onChange={(value, location?: LocationItem) => {
                setValue("pickup", value);
                if (location) {
                  // Cập nhật pickup trong useSearchStore
                  setPickup(location);

                  // Cập nhật origin trong useLocationStore
                  setOrigin(location);
                  setPickupSelected(true);
                  clearErrors("pickup");
                } else {
                  setPickupSelected(false);
                }
              }}
              placeholder={t("searchForm.pickup", "Pickup location")}
              error={errors.pickup?.message}
              className="h-12"
              isPickup={true}
              isOrigin={true}
            />
          </div>

          <div
            className={`flex items-center self-center h-full pt-6 mr-2 z-10 ${
              errors.pickup ? "mb-5" : ""
            }`}
          >
            <button
              onClick={(e) => {
                e.preventDefault();
                swapLocations();
                // Update selection state when swapping
                const tempPickupSelected = pickupSelected;
                setPickupSelected(destinationSelected);
                setDestinationSelected(tempPickupSelected);
              }}
              className="bg-gray-100 hover:bg-gray-200 p-2 rounded-full cursor-pointer shadow-sm transition-colors"
              aria-label={t(
                "searchForm.swapLocations",
                "Swap pickup and destination"
              )}
            >
              <SwapIcon />
            </button>
          </div>
        </div>

        <div className="flex-1">
          <label className="block text-sm font-medium text-gray-700 py-1 mb-1">
            {t("searchForm.destination", "Destination")}{" "}
            <span className="text-red-500">*</span>
          </label>
          <LocationInput
            value={destination}
            onChange={(value, location?: LocationItem) => {
              setValue("destination", value);
              if (location) {
                setDestination(location);

                setLocationStoreDestination(location);
                setDestinationSelected(true);
                clearErrors("destination");
              } else {
                setDestinationSelected(false);
              }
            }}
            placeholder={t("searchForm.destination", "Destination")}
            error={errors.destination?.message}
            className="h-12"
            isPickup={false}
          />
        </div>

        <DateTimePicker
          dateControl={control}
          timeControl={control}
          errors={errors}
        />

        <div className="w-full mb-1">
          <Button
            className="w-full h-12 bg-[#07F668] hover:bg-[#00C853] text-stone-950 rounded-full font-medium cursor-pointer"
            onClick={handleFormSubmit}
          >
            {t("searchForm.search", "Search")}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default BookingFormByRoute;
