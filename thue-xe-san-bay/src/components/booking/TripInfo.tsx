import { CarIcon, UserIcon } from "../../assets/icons";
import { ChevronDown, Clock, Locate, MapIcon, MapPin } from "lucide-react";
import React, { useState } from "react";

import { CustomImage } from "../ui/customImage";
import { useBookingModeStore } from "@/stores/useBookingModeStore";
import { useHourlyBookingStore } from "@/stores/useHourlyBookingStore";
import { useSearchStore } from "@/stores";
import { useTranslation } from "react-i18next";

interface TripInfoProps {
  selectedCar: string;
  selectedPrice?: number | null;
  getFormattedPrice?: (price: number, priceUSD?: number) => string;
}

const TripInfo: React.FC<TripInfoProps> = ({
  selectedCar,
  selectedPrice,
  getFormattedPrice,
}) => {
  const { pickup, destination, time, date } = useSearchStore();
  const { mode, pricingList } = useBookingModeStore();
  const { duration, getFormattedPrice: hourlyGetFormattedPrice } =
    useHourlyBookingStore();
  const { i18n, t } = useTranslation();
  const [open, setOpen] = useState(true);
  const vehicleType = selectedCar.includes("8") ? "VF8" : "VF9";
  const isHourlyMode = mode === "hourly";

  // Array object for surcharge mapping
  const surchargeData = [
    {
      labelKey: "tripInfo.surcharges.doorOpeningFee",
      priceEn: 4,
      priceVn: 100000,
      unitEn: "tripInfo.surcharges.pricePerMinutes",
      unitVn: "tripInfo.surcharges.pricePerKm",
      noteKey: "tripInfo.surcharges.doorOpeningFeeNote",
    },
    {
      labelKey: "tripInfo.surcharges.km4to30",
      priceEn: 4,
      priceVn: 25000,
      unitEn: "tripInfo.surcharges.pricePerMinutes",
      unitVn: "tripInfo.surcharges.pricePerKm",
    },
    {
      labelKey: "tripInfo.surcharges.km31plus",
      priceEn: 4,
      priceVn: 20000,
      unitEn: "tripInfo.surcharges.pricePerMinutes",
      unitVn: "tripInfo.surcharges.pricePerKm",
    },
  ];

  // Tìm thông tin xe đã chọn trong pricingList
  const selectedCarInfo = pricingList.find((car) => car.model === selectedCar);

  // Chuyển đổi roundedMinutes thành định dạng giờ phút
  const formatTravelTime = (minutes?: number): string => {
    if (!minutes) return "1h";

    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;

    if (mins === 0) {
      return `${hours}h`;
    } else {
      return `${hours}h ${mins}m`;
    }
  };

  // Format thời gian theo định dạng "8:00 - 06/06/2025"
  const formatPickupTime = (timeStr: string, dateObj: Date): string => {
    if (!timeStr || !dateObj) return timeStr;

    const day = String(dateObj.getDate()).padStart(2, "0");
    const month = String(dateObj.getMonth() + 1).padStart(2, "0");
    const year = dateObj.getFullYear();

    return `${timeStr} - ${day}/${month}/${year}`;
  };

  return (
    <>
      <h3 className="font-semibold text-[20px] leading-[150%] mb-4 md:mb-12">
        {t("tripInfo.title", "Hành trình của bạn")}
      </h3>
      <div className="bg-white rounded-lg p-4 shadow-md border border-gray-200">
        {/* Step vertical */}
        <div className="mt-4">
          <div className="relative">
            {/* Vertical line */}
            <div
              className={`absolute left-[15px] top-5 ${
                isHourlyMode ? "bottom-0" : "bottom-0"
              } w-0.5 bg-gray-300 z-0`}
            ></div>

            {/* Pickup point */}
            <div className="flex items-start mb-6 relative">
              <div className="flex-shrink-0 w-8 h-8 flex items-center justify-center bg-[#E5ECEC] rounded-full mr-3">
                <MapPin className="h-5 w-5" />
              </div>
              <div>
                <h4 className="font-semibold text-md text-stone-950">
                  {pickup}
                </h4>
              </div>
            </div>

            {/* Destination point - Only show in journey mode */}
            {!isHourlyMode && (
              <div className="flex items-start relative text-[16px]">
                <div className="flex-shrink-0 w-8 h-8 flex items-center justify-center bg-[#E5ECEC] rounded-full mr-3">
                  <Locate className="h-5 w-5" />
                </div>
                <div>
                  <h4 className="font-semibold text-md text-stone-950">
                    {destination}
                  </h4>
                </div>
              </div>
            )}
          </div>

          {/* Divider */}
          <div className="w-full h-[1px] bg-gray-200 my-4"></div>

          {/* Pickup time */}
          <div className="flex relative z-10 mt-8 ml-2 items-center">
            <Clock className="w-5 h-5 mr-5" />
            <h5 className="text-base font-[400] text-black">
              {t("tripInfo.pickupTime2", "Giờ đón")}: {formatPickupTime(time, date)}
            </h5>
          </div>

          {/* Distance - Only show in journey mode */}
          {!isHourlyMode && (
            <div className="flex relative z-10 mt-4 ml-2 items-center">
              <MapIcon className="w-5 h-5 mr-5" />
              <h5 className="text-base font-[400] text-black">
                {t("tripInfo.distance", "Quãng đường")}:{" "}
                {selectedCarInfo?.distance || "~"} km
              </h5>
            </div>
          )}

          {/* Estimated travel time */}
          <div className="flex relative z-10 mt-4 ml-2 items-center">
            <Clock className="w-5 h-5 mr-5" />
            <h5 className="text-base font-[400] text-black">
              {isHourlyMode
                ? `${t("tripInfo.rentalDuration", "Thời gian thuê")}: ${
                    duration === "0.5"
                      ? t("booking.duration0.5", "30 minutes")
                      : t(`booking.duration${duration}`, `${duration} hours`)
                  }`
                : `${t(
                    "tripInfo.estTimeDuration",
                    "Thời gian di chuyển dự kiến"
                  )}: ${formatTravelTime(selectedCarInfo?.roundedMinutes)}`}
            </h5>
          </div>
        </div>

        <div className="mt-4 border border-gray-200 rounded-lg p-4">
          <div className="flex items-center">
            <CustomImage
              src={
                vehicleType === "VF8"
                  ? "/images/vinfast/vf8.png"
                  : "/images/vinfast/vf9.png"
              }
              className="w-28 h-24 object-contain mr-3"
            />
            <div>
              <h4 className="font-bold">
                {selectedCar === "VF8" ? "Business" : "First Class"}
              </h4>
              <div className="flex flex-wrap items-center gap-4 text-sm mt-1">
                <div className="flex items-center gap-1">
                  <CarIcon />
                  <span>
                    {vehicleType === "VF8"
                      ? t("carCard.features.vf8.type", "D-SUV")
                      : t("carCard.features.vf9.type", "E-SUV")}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <UserIcon />
                  <span>
                    {vehicleType === "VF8"
                      ? t("carCard.features.vf8.seats", "5 chỗ")
                      : t("carCard.features.vf9.seats", "6-7 chỗ")}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-4 border border-gray-200 p-4 rounded-lg">
          <div className="flex flex-wrap justify-between items-center">
            <span className="text-md font-[400] mb-2 sm:mb-0">
              {t("tripInfo.paymentAmount", "Số tiền thanh toán")}
            </span>
            <div className="text-right">
              <span className="font-medium text-[20px] text-stone-950">
                {selectedPrice
                  ? getFormattedPrice
                    ? getFormattedPrice(
                        selectedPrice,
                        selectedCarInfo?.priceUSD
                      )
                    : hourlyGetFormattedPrice(
                        selectedPrice,
                        selectedCarInfo?.priceUSD
                      )
                  : ""}
              </span>
              <p className="text-xs text-[#3A3938] mt-0.5 font-[400]">
                {t("tripInfo.priceIncludesVAT", "Giá đã bao gồm VAT")}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-[#F6F6F6] border rounded-xl shadow-sm p-4 w-full text-sm mt-4">
          <div
            className="flex justify-between items-start cursor-pointer"
            onClick={() => setOpen(!open)}
          >
            <p className="text-gray-700">
              {t("tripInfo.additionalCharges", "* Giá có thể có thêm phụ phí")}
            </p>
            <ChevronDown
              className={`text-gray-500 transition-transform duration-300 ${
                open ? "rotate-180" : "rotate-0"
              }`}
            />
          </div>

          {open && (
            <div>
              <div className="bg-white mt-4 border rounded-md overflow-hidden">
                {surchargeData.map((item, index) => (
                  <div
                    key={index}
                    className="px-4 py-2 border-t text-sm"
                  >
                    <div className="grid grid-cols-2">
                      <div className="text-[16px]">
                        {t(item.labelKey)}
                        {item.noteKey && (
                          <div className="text-xs text-gray-500 mt-1">
                            {t(item.noteKey)}
                          </div>
                        )}
                      </div>
                      <div className="font-medium text-right">
                        {i18n.language === "en"
                          ? `+ ${t(item.unitEn, {
                              price: new Intl.NumberFormat("en-US").format(
                                item.priceEn
                              ),
                            })}`
                          : `+ ${t(item.unitVn, {
                              price: new Intl.NumberFormat("vi-VN").format(
                                item.priceVn
                              ),
                            })}`}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default TripInfo;
