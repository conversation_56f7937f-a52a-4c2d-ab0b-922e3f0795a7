import React from "react";
import BookingForm from "./BookingForm";
import DialogWrapper from "../ui/dialog-wrapper";
import { useMediaQuery } from "@/hooks/use-media-query";

interface BookingModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedCar: string;
  selectedSku?: string | null;
  selectedQuoteToken?: string | null;
  onSuccess?: () => void;
}

const BookingModal: React.FC<BookingModalProps> = ({
  isOpen,
  onClose,
  selectedCar,
  selectedSku,
  selectedQuoteToken,
  onSuccess,
}) => {
  const isDesktop = useMediaQuery("(min-width: 1280px)"); // lg breakpoint

  const handleBookingSuccess = () => {
    // Call success callback if provided
    if (onSuccess) {
      onSuccess();
    }
    // Close the modal
    onClose();
  };

  const handleGoBack = () => {
    // Close booking modal to go back to car selection
    onClose();
  };

  return (
    <DialogWrapper 
      isOpen={isOpen} 
      onClose={onClose}
      mobileFullHeight={true}
      backgroundClass={isDesktop ? "bg-white" : "bg-black"}
      maxWidth="1200px"
    >
      <BookingForm
        selectedCar={selectedCar}
        selectedSku={selectedSku}
        selectedQuoteToken={selectedQuoteToken}
        onGoBack={handleGoBack}
        onSuccess={handleBookingSuccess}
      />
    </DialogWrapper>
  );
};

export default BookingModal;
