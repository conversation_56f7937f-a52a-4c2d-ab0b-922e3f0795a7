import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import dayjs from "dayjs";
import React, { useCallback, useEffect } from "react";
import { useGoogleReCaptcha } from "react-google-recaptcha-v3";
import { useForm, Controller } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { postBooking, BookingRequestInput } from "../../api/services/booking";
import { useUtmParams } from "../../hooks";
import { useLoadingStore, useSearchStore } from "../../stores";
import { useHourlyBookingStore } from "../../stores/useHourlyBookingStore";
import { useBookingModeStore } from "../../stores/useBookingModeStore";
import { useLocationStore } from "../../stores/useLocationStore";
import {
  createBookingFormSchema,
  type BookingFormValues,
} from "../../lib/validations";
import { But<PERSON> } from "../ui/button";
import { <PERSON><PERSON><PERSON>ead<PERSON>, DialogTitle } from "../ui/dialog";
import TripInfo from "./TripInfo";
import 'react-international-phone/style.css';
import '../../styles/phone-input.css';
import { parsePhoneNumberWithError } from 'libphonenumber-js';
import SearchableCountrySelector from '../ui/SearchableCountrySelector';


interface BookingFormProps {
  selectedCar: string;
  selectedSku?: string | null;
  selectedQuoteToken?: string | null;
  onGoBack: () => void;
  onSuccess: () => void;
}

const BookingForm: React.FC<BookingFormProps> = ({
  selectedCar,
  selectedSku,
  selectedQuoteToken,
  onSuccess,
}) => {
  const { pickup, destination, date, time, pickUpLocation, destinationLocation } = useSearchStore();
  const { setLoading } = useLoadingStore();
  const { t } = useTranslation();
  const { mode } = useBookingModeStore();
  const { getSelectedVehiclePrice, getFormattedPrice } = useHourlyBookingStore();
  const { setOrigin, setDestination: setLocationDestination } = useLocationStore();
  const [isSubmitting, setIsSubmitting] = React.useState<boolean>(false);

  // Determine if we're in hourly mode
  const isHourlyMode = mode === 'hourly';

  // Get the price based on the selected car
  // Lấy giá từ pricingList cho cả hai chế độ
  const { pricingList } = useBookingModeStore();
  const selectedCarPricing = pricingList.find(item => item.model === selectedCar);
  const selectedPrice = selectedCarPricing?.price || (isHourlyMode ? getSelectedVehiclePrice() : null);

  // Lấy các tham số UTM từ URL
  const utmParams = useUtmParams();

  // Tạo schema mới mỗi khi ngôn ngữ thay đổi
  const bookingSchema = React.useMemo(() => {
    return createBookingFormSchema();
  }, []);

  // Form book xe validation
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
    control, // Thêm control để sử dụng với Controller
  } = useForm<BookingFormValues>({
    resolver: zodResolver(bookingSchema),
    defaultValues: {
      fullName: "",
      phoneNumber: "",
      email: "",
      notes: "",
      flightCode: "",
      pickupCode: pickup,
      destinationCode: destination,
      pickupDateTime: "",
      acceptTerms: false,
    },
  });

  const { executeRecaptcha } = useGoogleReCaptcha();

  // Format số điện thoại theo yêu cầu với libphonenumber-js
  const formatPhoneNumber = (phone: string) => {
    if (!phone) return "";

    try {
      // Parse the phone number with libphonenumber-js
      const phoneNumber = parsePhoneNumberWithError(phone);

      if (phoneNumber && phoneNumber.isValid()) {
        // Return in E.164 format (international format with +)
        return phoneNumber.format('E.164');
      }
    } catch {
      // If parsing fails, fallback to original logic
    }

    // Fallback formatting logic for edge cases
    if (phone.startsWith("+")) {
      // Clean up the phone number by removing spaces and non-digits except +
      return phone.replace(/[^\d+]/g, '');
    }

    // If no + prefix, assume it needs one
    const cleanPhone = phone.replace(/\D/g, "");
    if (cleanPhone.startsWith('0')) {
      return "+" + cleanPhone.substring(1);
    }
    return "+" + cleanPhone;
  };

  // Process book car
  const onBookCar = async (data: BookingFormValues) => {
    if (!executeRecaptcha) {
      return;
    }
    setIsSubmitting(true);
    // Show loading
    setLoading(true, t("booking.processing", "Đang xử lý đặt xe..."));

    try {
      // Create dayjs object from selected date and time
      const [hours, minutes] = time.split(":").map(Number);

      // Tạo một bản sao của ngày đã chọn để tránh ảnh hưởng đến state
      const selectedDate = new Date(date);

      // Create dayjs object and set hours, minutes
      // Sử dụng selectedDate thay vì date trực tiếp
      const localDatetime = dayjs(selectedDate).hour(hours).minute(minutes).second(0);

      // Convert to UTC
      const pickupDateTimeUTC = localDatetime.utc().format();

      // Chuẩn bị dữ liệu liên hệ
      const contactData = {
        phone_number: formatPhoneNumber(data.phoneNumber),
        email: data.email || undefined,
      };

      // Đồng bộ thông tin từ useSearchStore sang useLocationStore
      if (pickUpLocation) {
        setOrigin(pickUpLocation);
      }

      if (destinationLocation) {
        setLocationDestination(destinationLocation);
      }

      // Call API booking
      const captchaToken = await executeRecaptcha("submitlead");

      // Tạo request với cấu trúc đầu vào
      const bookingRequest: BookingRequestInput = {
        full_name: data.fullName,
        ...contactData,
        notes: data.notes || undefined,
        flight_code: data.flightCode || undefined,
        pickup_code: pickup,
        destination_code: destination,
        pickup_date_time: pickupDateTimeUTC,
        vehicle_type: selectedCar,
        sku: selectedSku || "",
        quoteToken: selectedQuoteToken || "",
        // Thêm rentalType dựa vào chế độ đặt xe
        rentalType: isHourlyMode ? "HOURLY" : "ROUTE",
        // Không cần thêm place_id nữa vì đã sử dụng address trong API
        // Thêm tham số UTM từ URL nếu có
        ...utmParams,
        // Vẫn giữ campaign_code từ env nếu không có campaign_code từ URL
        campaign_code:
          utmParams.campaign_code || import.meta.env.VITE_CAMPAIGN_CODE,
      };

      // Gọi API, hàm postBooking sẽ chuyển đổi sang cấu trúc mới
      const response = await postBooking(bookingRequest, captchaToken);

      if (response.error) {
        console.error("Error during booking:", response.error);
        // Show error message
      } else {
        // Reset form and show success message
        reset();
        // Gọi onSuccess để thông báo cho component cha
        onSuccess();
      }
    } catch (error) {
      console.error("Failed to submit booking:", error);
      // Show error message
    } finally {
      setIsSubmitting(false);
      setLoading(false);
    }
  };

  const handleReCaptchaVerify = useCallback(async () => { }, []);

  useEffect(() => {
    handleReCaptchaVerify();
  }, [handleReCaptchaVerify]);

  useEffect(() => {
    handleReCaptchaVerify();
  }, [handleReCaptchaVerify]);

  return (
    <div className="">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 md:p-0">
        <div className="md:order-1">
          <DialogHeader>
            <DialogTitle className="!text-left text-black">
              {t("booking.bookingTitle", "Đặt dịch vụ")}
            </DialogTitle>
          </DialogHeader>
          <div className="md:py-4 mt-4">
            <div className="space-y-4 md:pb-24">
              <form id="bookingForm" onSubmit={handleSubmit(onBookCar)} className="space-y-4">
                <div>
                  <label
                    htmlFor="fullName"
                    className="block text-sm font-medium mb-1 text-gray-700"
                  >
                    {t("booking.fullName", "Họ và tên")}{" "}
                    <span className="text-red-500">*</span>
                  </label>
                  <input
                    id="fullName"
                    {...register("fullName")}
                    className={`w-full h-12 px-4 rounded-md border ${errors.fullName
                        ? "border-red-500 ring-1 ring-red-500"
                        : "border-gray-300"
                      } focus:outline-none focus:ring-2 focus:ring-[#07F668] bg-white text-black`}
                    placeholder={t(
                      "booking.enterFullName",
                      "Nhập họ và tên của bạn"
                    )}
                  />
                  {errors.fullName && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.fullName.message}
                    </p>
                  )}
                </div>

                <div>
                  <label
                    htmlFor="phoneNumber"
                    className="block text-sm font-medium mb-1 text-gray-700"
                  >
                    {t("booking.phoneNumber", "Số điện thoại")}{" "}
                    <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <Controller
                      name="phoneNumber"
                      control={control}
                      render={({ field }) => (
                        <SearchableCountrySelector
                          value={field.value}
                          onChange={(value) => field.onChange(value)}
                          error={!!errors.phoneNumber}
                          placeholder={t(
                            "booking.enterPhoneNumberIntl",
                            "Nhập số điện thoại"
                          )}
                          className="phone-input-focus-wrapper"
                        />
                      )}
                    />
                  </div>
                  {errors.phoneNumber && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.phoneNumber.message}
                    </p>
                  )}
                </div>

                <div>
                  <label
                    htmlFor="email"
                    className="block text-sm font-medium mb-1 text-gray-700"
                  >
                    {t("booking.email", "Email")}{" "}
                    <span className="text-gray-500 text-xs font-normal">{t("booking.optional", "(Tùy chọn)")}</span>
                  </label>
                  <input
                    id="email"
                    type="email"
                    {...register("email")}
                    className={`w-full h-12 px-4 rounded-md border ${errors.email
                        ? "border-red-500 ring-1 ring-red-500"
                        : "border-gray-300"
                      } focus:outline-none focus:ring-2 focus:ring-[#07F668] bg-white text-black`}
                    placeholder={t(
                      "booking.enterEmail",
                      "Nhập địa chỉ email"
                    )}
                  />
                  {errors.email && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.email.message}
                    </p>
                  )}
                </div>

                <div>
                  <label
                    htmlFor="flightCode"
                    className="block text-sm font-medium mb-1 text-gray-700"
                  >
                    {t("booking.flightCode", "Mã chuyến bay")}
                  </label>
                  <input
                    id="flightCode"
                    {...register("flightCode")}
                    className="w-full h-12 px-4 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#07F668] bg-white text-black"
                    placeholder={t(
                      "booking.enterFlightCode",
                      "Nhập mã chuyến bay của bạn"
                    )}
                  />
                </div>

                <div>
                  <label
                    htmlFor="notes"
                    className="block text-sm font-medium mb-1 text-gray-700"
                  >
                    {t("booking.notes", "Lời nhắn")}
                  </label>
                  <textarea
                    id="notes"
                    {...register("notes")}
                    rows={3}
                    className="w-full px-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#07F668] bg-white text-black"
                    placeholder={t(
                      "booking.enterNotes",
                      "Nhập lời nhắn hoặc yêu cầu đặc biệt"
                    )}
                  />
                </div>

                {/* Policy section - visible only on desktop */}
                <div className="hidden md:block">
                  <div className="space-y-4 p-2 rounded-md bg-gray-100">
                    <div className="flex items-start">
                      <div className="flex-shrink-0 w-5 h-5 mt-0.5 mr-2">
                        <input
                          type="checkbox"
                          id="acceptTerms"
                          className="h-5 w-5 rounded border-gray-300 text-[#07F668] focus:ring-[#07F668] accent-[#07F668]"
                          onChange={(e) => setValue("acceptTerms", e.target.checked, { shouldValidate: true })}
                          checked={watch("acceptTerms")}
                        />
                      </div>
                      <label
                        htmlFor="acceptTerms"
                        className="text-sm text-black leading-snug"
                      >
                        Tôi xác nhận đã đọc hiểu và đồng ý với{" "}
                        <a
                          href="https://greenfuture.tech/policy/privacies-policy"
                          target="_blank"
                          className="text-black hover:text-[#07F668] underline"
                        >
                          {t("booking.generalTerms", "Điều khoản chung")}
                        </a>{" "}
                        và{" "}
                        <a
                          href="https://greenfuture.tech/policy/payment-terms"
                          target="_blank"
                          className="text-black hover:text-[#07F668] underline"
                        >
                          {t("booking.paymentTerms", "Điều khoản thanh toán")}
                        </a>{" "}
                        của Green Future.
                      </label>
                    </div>
                    {errors.acceptTerms && (
                      <p className="text-red-500 text-sm ml-7">
                        {errors.acceptTerms.message}
                      </p>
                    )}
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>

        <div className="md:order-2 order-last">
          <TripInfo
            selectedCar={selectedCar}
            selectedPrice={selectedPrice}
            getFormattedPrice={getFormattedPrice}
          />
        </div>
      </div>

      {/* Policy section - visible only on mobile */}
      <div className="block md:hidden mb-4">
        <div className="space-y-4 p-2 rounded-md bg-gray-800">
          <div className="flex items-start">
            <div className="flex-shrink-0 w-5 h-5 mt-0.5 mr-2">
              <input
                type="checkbox"
                id="acceptTermsMobile"
                className="h-5 w-5 rounded border-gray-300 text-[#07F668] focus:ring-[#07F668] accent-[#07F668]"
                onChange={(e) => setValue("acceptTerms", e.target.checked, { shouldValidate: true })}
                checked={watch("acceptTerms")}
              />
            </div>
            <label
              htmlFor="acceptTermsMobile"
              className="text-sm text-white leading-snug"
            >
              Tôi xác nhận đã đọc hiểu và đồng ý với{" "}
              <a
                href="/policy/privacies-policy"
                target="_blank"
                className="text-white hover:text-[#07F668] underline"
              >
                {t("booking.generalTerms", "Điều khoản chung")}
              </a>{" "}
              và{" "}
              <a
                href="/policy/payment-terms"
                target="_blank"
                className="text-white hover:text-[#07F668] underline"
              >
                {t("booking.paymentTerms", "Điều khoản thanh toán")}
              </a>{" "}
              của Green Future.
            </label>
          </div>
          {errors.acceptTerms && (
            <p className="text-red-500 text-sm ml-7">
              {errors.acceptTerms.message}
            </p>
          )}
        </div>
      </div>

      {/* Payment footer - moved outside the grid */}
      <div className="left-0 right-0 w-full">
        <div className="p-2 bg-[#F2F2F7]">
          <Button
            type="submit"
            form="bookingForm"
            className="h-12 w-full bg-[#07F668] hover:bg-[#00C853] text-[20px] font-medium text-[#00423E] rounded-full cursor-pointer px-8"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center">
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-stone-950 border-t-transparent"></div>
                {t("booking.processing", "Đang xử lý...")}
              </div>
            ) : (
              t("booking.bookCar")
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default BookingForm;
