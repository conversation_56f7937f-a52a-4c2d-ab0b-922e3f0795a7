import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  ogType?: string;
  ogImage?: string;
  ogUrl?: string;
  canonicalUrl?: string;
}

const SEO = ({
  title,
  description,
  keywords,
  ogType = 'website',
  ogImage = '/images/logo/logo-gf.svg',
  ogUrl,
  canonicalUrl,
}: SEOProps) => {
  const { i18n } = useTranslation();
  const isVietnamese = i18n.language === 'vi';

  // SEO content based on language
  const defaultTitle = isVietnamese
    ? 'Dịch vụ thuê xe sân bay uy tín, giá rẻ | Green Future'
    : 'Reliable Airport Car Rental Service | Green Future';

  const defaultDescription = isVietnamese
    ? 'Dịch vụ thuê xe sân bay uy tín, gi<PERSON> cả hợp lý. Đón sân bay Tân Sơn Nhất, Nội Bài, Đà Nẵng. Đặt xe nhanh chóng, tài xế chuyên nghiệp, xe đời mới.'
    : 'Reliable airport car rental service at affordable prices. Pickup from Tan Son Nhat, Noi Bai, Da Nang airports. Fast booking, professional drivers, new vehicles.';

  const defaultKeywords = isVietnamese
    ? 'thuê xe sân bay, đón sân bay, đưa đón sân bay, taxi sân bay, xe đón sân bay, Green Future, dịch vụ đón sân bay, đón tiễn sân bay'
    : 'airport car rental, airport pickup, airport transfer, airport taxi, airport car service, Green Future, airport shuttle, airport transportation';

  // URL patterns based on language
  const basePath = '/thue-xe-san-bay'; // Base path on your domain
  const defaultOgUrl = isVietnamese
    ? `${basePath}`
    : `${basePath}/en`;

  const defaultCanonicalUrl = isVietnamese
    ? `${basePath}`
    : `${basePath}/en`;

  // Use provided props or defaults
  const seoTitle = title || defaultTitle;
  const seoDescription = description || defaultDescription;
  const seoKeywords = keywords || defaultKeywords;
  const seoOgUrl = ogUrl || defaultOgUrl;
  const seoCanonicalUrl = canonicalUrl || defaultCanonicalUrl;

  return (
    <Helmet>
      {/* Tiêu đề và mô tả cơ bản */}
      <html lang={i18n.language} />
      <title>{seoTitle}</title>
      <meta name="description" content={seoDescription} />
      <meta name="keywords" content={seoKeywords} />

      {/* Open Graph Meta Tags cho mạng xã hội */}
      <meta property="og:title" content={seoTitle} />
      <meta property="og:description" content={seoDescription} />
      <meta property="og:type" content={ogType} />
      <meta property="og:url" content={seoOgUrl} />
      <meta property="og:image" content={ogImage} />

      {/* Twitter Card data */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={seoTitle} />
      <meta name="twitter:description" content={seoDescription} />
      <meta name="twitter:image" content={ogImage} />

      {/* Canonical URL */}
      <link rel="canonical" href={seoCanonicalUrl} />

      {/* Thẻ meta bổ sung */}
      <meta name="author" content="Green Future" />
      <meta name="robots" content="index, follow" />

      {/* Hỗ trợ đa ngôn ngữ */}
      <link rel="alternate" href={basePath} hrefLang="vi-vn" />
      <link rel="alternate" href={`${basePath}/en`} hrefLang="en-us" />
      <link rel="alternate" href={basePath} hrefLang="x-default" />
    </Helmet>
  );
};

export default SEO; 