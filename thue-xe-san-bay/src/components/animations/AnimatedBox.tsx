import { motion } from 'framer-motion';

interface AnimatedBoxProps {
  text: string;
}

const AnimatedBox = ({ text }: AnimatedBoxProps) => {
  return (
    <motion.div
      className="bg-blue-500 text-white p-4 rounded-lg shadow-lg"
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      {text}
    </motion.div>
  );
};

export default AnimatedBox; 