import { BoxIcon, CarIcon, UserIcon } from "../../assets/icons";
import { Clock, Locate, MapIcon, MapPin, X } from "lucide-react";
import { enUS, vi } from "date-fns/locale";
import { useEffect, useState } from "react";

import { But<PERSON> } from "../ui/button";
import { CheckIcon } from "lucide-react";
import { CustomImage } from "../ui/customImage";
import { format } from "date-fns";
import { useBookingModeStore } from "@/stores/useBookingModeStore";
import { useHourlyBookingStore } from "@/stores/useHourlyBookingStore";
import { useSearchStore } from "@/stores/useSearchStore";
import { useTranslation } from "react-i18next";

interface CarSelectionMobileProps {
  onSelectCar: (carType: string, sku?: string, quoteToken?: string) => void;
  onClose?: () => void;
  isInDialog?: boolean;
}

const CarSelectionMobile = ({
  onSelectCar,
  onClose,
  isInDialog = false,
}: CarSelectionMobileProps) => {
  const { t, i18n } = useTranslation();
  const { duration, getFormattedPrice } = useHourlyBookingStore();
  const { mode, pricingList } = useBookingModeStore();
  const { pickup, destination, date, time } = useSearchStore();
  const isHourlyMode = mode === "hourly";

  // Function to format date based on language
  const getFormattedDate = (date: Date) => {
    const dateFormat = i18n.language === "en" ? "MM/dd/yyyy" : "dd/MM/yyyy";
    return format(date, dateFormat, {
      locale: i18n.language === "en" ? enUS : vi,
    });
  };

  // Chuyển đổi roundedMinutes thành định dạng giờ phút
  const formatTravelTime = (minutes?: number): string => {
    if (!minutes) return "1h";

    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;

    if (mins === 0) {
      return `${hours}h`;
    } else {
      return `${hours}h ${mins}m`;
    }
  };

  // State for car data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [carData, setCarData] = useState<any[]>([]);

  // Get vehicle prices based on duration or journey pricing
  useEffect(() => {
    // Sử dụng dữ liệu từ API cho cả hai chế độ
    if (pricingList && pricingList.length > 0) {
      // Map API response to car data format
      const cars = pricingList.map((item) => ({
        id: item.model === "VF8" ? 1 : 2,
        model: item.model,
        price: item.price,
        priceUSD: item.priceUSD,
        formattedPrice: getFormattedPrice(item.price, item.priceUSD),
        image: `/images/vinfast/${item.model.toLowerCase()}.png`,
        vehicleType: item.model as "VF8" | "VF9",
        duration: isHourlyMode ? duration : null,
        roundedMinutes: item.roundedMinutes,
        distance: item.distance,
        priceBlock: item.priceBlock,
        sku: item.sku,
        quoteToken: item.quoteToken,
      }));
      setCarData(cars);
    } else {
      // Không sử dụng dữ liệu giả nữa, để trống để người dùng biết cần gọi API
      setCarData([]);
    }
  }, [pricingList, isHourlyMode, duration, getFormattedPrice]);

  return (
    <div className={isInDialog ? "w-full h-full flex flex-col" : "fixed inset-0 bg-black z-50"}>
      {/* Header with close button */}
      <div className={`${isInDialog ? "flex-shrink-0" : "top-0"} bg-black p-4 flex items-center justify-between`}>
        <h1 className="text-white text-lg font-semibold">
          {t(
            "tripInfo.serviceTitle",
            "Lựa chọn dịch vụ đưa đón sân bay GF VIP"
          )}
        </h1>
        {onClose && (
          <button
            onClick={onClose}
            className="text-white p-2 hover:bg-gray-800 rounded-full transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        )}
      </div>

      <div className={`p-4 space-y-6 ${isInDialog ? "flex-1 overflow-y-auto" : "overflow-y-auto"}`}>
        {/* Trip Information Display */}
        <div className="bg-white rounded-lg p-4">
          <div className="space-y-4">
            {/* Location information */}
            <div className="relative">
              {/* Vertical line - Only show if destination exists */}
              {!isHourlyMode && destination && (
                <div className="absolute left-[11px] top-6 bottom-2 w-0.5 bg-gray-600 z-0"></div>
              )}

              {/* Pickup point */}
              <div className="flex items-start mb-4 relative z-10">
                <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center bg-gray-700 rounded-full mr-3">
                  <MapPin className="h-4 w-4 text-white" />
                </div>
                <div className="flex-1">
                  <p className="font-medium text-sm text-[#090806]">{pickup}</p>
                </div>
              </div>

              {/* Destination point - Only show if not hourly mode */}
              {!isHourlyMode && destination && (
                <div className="flex items-start relative z-10">
                  <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center bg-gray-700 rounded-full mr-3">
                    <Locate className="h-4 w-4 text-white" />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-sm text-[#090806]">
                      {destination}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Divider */}
            <div className="h-px bg-[#CECECD]"></div>

            {/* Trip details */}
            <div className="space-y-2">
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-2 text-gray-400" />
                <span className="font-medium text-sm text-[#090806]">
                  {t("tripInfo.pickupTime2", "Giờ đón")}:{" "}
                  <span className="font-normal text-[#090806]">
                    {time} - {getFormattedDate(date)}
                  </span>
                </span>
              </div>
              {!isHourlyMode && (
                <>
                  <div className="flex items-center">
                    <MapIcon className="w-4 h-4 mr-2 text-gray-400" />
                    <span className="font-medium text-sm text-[#090806]">
                      {t("tripInfo.distance", "Quãng đường")}: ~{" "}
                      <span className="font-normal text-[#090806]">
                        {carData[0]?.distance} km
                      </span>
                    </span>
                  </div>
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 mr-2 text-gray-400" />
                    <span className="font-medium text-sm text-[#090806]">
                      Thời gian di chuyển:{" "}
                      <span className="font-normal text-[#090806]">
                        ~ {formatTravelTime(carData[0]?.roundedMinutes)}
                      </span>
                    </span>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Car Selection */}
        <div className="space-y-4">
          {carData.map((car) => (
            <div
              key={car.id}
              className="bg-white rounded-2xl p-4 border border-gray-800"
            >
              <div className="space-y-4">
                {/* Car Image */}
                <div className="flex justify-center">
                  <CustomImage
                    src={car.image}
                    className="w-full h-auto object-contain max-h-[150px]"
                  />
                </div>

                {/* Title and Price */}
                <div className="flex justify-between items-center">
                  <div className="bg-black px-3 py-1 text-white text-[20px] font-[Tektur] font-bold rounded-xs">
                    {car.model === "VF8" ? "BUSINESS" : "FIRST CLASS"}
                  </div>
                  <div className="text-right">
                    <span className="text-[20px] font-medium text-black">
                      {car.formattedPrice}
                    </span>
                  </div>
                </div>

                {/* Car Details */}
                <div className="grid grid-cols-3 gap-2 rounded-lg">
                  <div className="flex flex-col items-start bg-[#E6E6E6] rounded p-1.5 pb-4">
                    <UserIcon className="w-4 h-4 text-gray-600 mb-1" />
                    <p className="text-[14px] text-[#090806]text-left">
                      {car.vehicleType === "VF8"
                        ? t("carCard.features.vf8.seats", "5 chỗ")
                        : t("carCard.features.vf9.seats", "6-7 chỗ")}
                    </p>
                  </div>
                  <div className="flex flex-col items-start bg-[#E6E6E6] rounded p-1">
                    <CarIcon className="w-4 h-4 text-gray-600 mb-1" />
                    <p className="text-[14px] text-[#090806]text-left">
                      {car.vehicleType === "VF8"
                        ? t("carCard.features.vf8.type", "D-SUV")
                        : t("carCard.features.vf9.type", "E-SUV")}
                    </p>
                  </div>
                  <div className="flex flex-col items-start bg-[#E6E6E6] rounded p-1">
                    <BoxIcon className="w-4 h-4 text-gray-600 mb-1" />
                    <p className="text-[14px] text-[#090806]text-left">
                      {car.vehicleType === "VF8"
                        ? t("carCard.features.vf8.trunk", "Cốp rộng rãi")
                        : t("carCard.features.vf9.trunk", "Cốp siêu rộng rãi")}
                    </p>
                  </div>
                </div>

                {/* Features List */}
                <div className="grid grid-cols-1 gap-2">
                  <div className="space-y-2">
                    <div className="flex items-start">
                      <CheckIcon className="h-4 w-4 text-[#07F668] mr-2 mt-0.5 flex-shrink-0" />
                      <p className="text-xs text-black">
                        {car.vehicleType === "VF8"
                          ? t("carCard.features.vf8.seats", "5 chỗ")
                          : t("carCard.features.vf9.seats", "6-7 chỗ")}
                      </p>
                    </div>
                    <div className="flex items-start">
                      <CheckIcon className="h-4 w-4 text-[#07F668] mr-2 mt-0.5 flex-shrink-0" />
                      <p className="text-xs text-black">
                        {t(
                          "carCard.features.airbags",
                          "Hệ thống bảo vệ 11 túi khí"
                        )}
                      </p>
                    </div>
                    <div className="flex items-start">
                      <CheckIcon className="h-4 w-4 text-[#07F668] mr-2 mt-0.5 flex-shrink-0" />
                      <p className="text-xs text-black">
                        {t(
                          "carCard.features.charging",
                          "Sạc điện thoại miễn phí"
                        )}
                      </p>
                    </div>
                    <div className="flex items-start">
                      <CheckIcon className="h-4 w-4 text-[#07F668] mr-2 mt-0.5 flex-shrink-0" />
                      <p className="text-xs text-black">
                        {t(
                          "carCard.features.driver",
                          "Tài xế tận tâm, chuyên nghiệp"
                        )}
                      </p>
                    </div>
                    <div className="flex items-start">
                      <CheckIcon className="h-4 w-4 text-[#07F668] mr-2 mt-0.5 flex-shrink-0" />
                      <p className="text-xs text-black">
                        {car.vehicleType === "VF8"
                          ? t("carCard.features.vf8.trunk", "Cốp xe rộng rãi")
                          : t(
                              "carCard.features.vf9.trunk",
                              "Cốp xe siêu rộng rãi"
                            )}
                      </p>
                    </div>
                    {car.vehicleType === "VF9" && (
                      <div className="flex items-start">
                        <CheckIcon className="h-4 w-4 text-[#07F668] mr-2 mt-0.5 flex-shrink-0" />
                        <p className="text-xs text-black">
                          {t(
                            "carCard.features.vf9.seats_heating",
                            "Hệ thống ghế sưởi, massage"
                          )}
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Select Button */}
                <Button
                  className="w-full h-12 bg-white border border-black text-[#090806] rounded-full text-[16px] font-semibold hover:bg-gray-200 transition-colors"
                  onClick={() =>
                    onSelectCar(car.vehicleType, car.sku, car.quoteToken)
                  }
                >
                  {t("carCard.selectButtonWithModel", "Chọn dịch vụ")}
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default CarSelectionMobile;
