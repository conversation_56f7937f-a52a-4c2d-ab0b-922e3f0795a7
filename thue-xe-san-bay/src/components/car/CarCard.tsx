import { BoxIcon, CarIcon, UserIcon } from "../../assets/icons";

import { Button } from "../ui/button";
import { CheckIcon } from "lucide-react";
import { CustomImage } from "../ui/customImage";
import React from "react";
import { convertMinutesToHours } from "@/lib/utils";
import { useTranslation } from "react-i18next";

interface CarCardProps {
  model: string;
  price: string;
  image: string;
  // movingTime: string;
  vehicleType?: "VF8" | "VF9";
  onSelect: () => void;
  time: string;
}

const CarCard: React.FC<CarCardProps> = ({
  model,
  price,
  image,
  // movingTime,
  vehicleType = "VF8",
  time,
  onSelect,
}) => {
  const { t } = useTranslation();
  const getMovingTime = () => {
    const { hour, mins } = convertMinutesToHours(Number(time) || 1);
    return `${hour ? hour : ""} ${
      hour ? t("tripInfo.hour", { count: hour }) : ""
    } ${mins ? mins : ""} ${
      mins ? t("tripInfo.minutes", { count: mins }) : ""
    } `;
  };
  return (
    <div className="bg-white rounded-lg p-4 shadow-md border border-gray-200">
      <div className="w-full h-full">
        {/* Information Car */}
        <div className="p-2">
          <CustomImage src={image} className="w-full h-full object-cover" />
          {/* Title and Price */}
          <div className="flex flex-col sm:flex-row justify-between font-[SVN-Poppins] font-medium">
            <h3 className="text-lg mb-2 sm:mb-0">VinFast {model}</h3>
            <p className="text-lg">
              <span className="text-md text-gray-500">
                {t("tripInfo.from", "from")}{" "}
              </span>
              {price}
            </p>
          </div>

          <div className="flex justify-between font-[SVN-Poppins] font-medium">
            <p className="text-[#848382] text-sm  mb-2 ">
              {t("tripInfo.estTimeDuration", "Thời gian di chuyển dự kiến")}
            </p>
            <p className="text-[#848382] text-sm mb-2 ">{getMovingTime()}</p>
          </div>

          {/* Information Car Detail */}
          <div className="flex flex-col sm:flex-row justify-between gap-2 sm:gap-0 bg-[#E5ECEC] rounded-lg p-2.5">
            <div className="flex items-center gap-1">
              <CarIcon />
              <p className="text-sm font-[400]">
                {vehicleType === "VF8"
                  ? t("carCard.features.vf8.type", "D-SUV")
                  : t("carCard.features.vf9.type", "E-SUV")}
              </p>
            </div>
            <div className="flex items-center gap-1">
              <UserIcon />
              <p className="text-sm font-[400]">
                {vehicleType === "VF8"
                  ? t("carCard.features.vf8.seats", "5 chỗ")
                  : t("carCard.features.vf9.seats", "6-7 chỗ")}
              </p>
            </div>
            <div className="flex items-center gap-1">
              <BoxIcon />
              <p className="text-sm font-[400]">
                {vehicleType === "VF8"
                  ? t("carCard.features.vf8.trunk", "Cốp rộng rãi")
                  : t("carCard.features.vf9.trunk", "Cốp siêu rộng rãi")}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-5">
            <div className="space-y-2 sm:space-y-3 md:space-y-4">
              <div className="flex min-h-[20px] sm:min-h-[24px] md:min-h-[24px] items-start">
                <div className="flex-shrink-0 w-4 sm:w-5 mr-1 sm:mr-1.5 pt-0.5">
                  <CheckIcon className="h-4 w-4 sm:h-5 sm:w-5 text-[#FFA6DD]" />
                </div>
                <p className="text-xs sm:text-sm font-[400] leading-[1.3] sm:leading-[1.4]">
                  {vehicleType === "VF8"
                    ? t("carCard.features.vf8.seats", "5 chỗ")
                    : t("carCard.features.vf9.seats", "6-7 chỗ")}
                </p>
              </div>
              <div className="flex min-h-[20px] sm:min-h-[24px] md:min-h-[24px] items-start">
                <div className="flex-shrink-0 w-4 sm:w-5 mr-1 sm:mr-1.5 pt-0.5">
                  <CheckIcon className="h-4 w-4 sm:h-5 sm:w-5 text-[#FFA6DD]" />
                </div>
                <p className="text-xs sm:text-sm font-[400] leading-[1.3] sm:leading-[1.4]">
                  {t("carCard.features.airbags", "Hệ thống bảo vệ 11 túi khí")}
                </p>
              </div>
              <div className="flex min-h-[20px] sm:min-h-[24px] md:min-h-[24px] items-start">
                <div className="flex-shrink-0 w-4 sm:w-5 mr-1 sm:mr-1.5 pt-0.5">
                  <CheckIcon className="h-4 w-4 sm:h-5 sm:w-5 text-[#FFA6DD]" />
                </div>
                <p className="text-xs sm:text-sm font-[400] leading-[1.3] sm:leading-[1.4]">
                  {t("carCard.features.charging", "Sạc điện thoại miễn phí")}
                </p>
              </div>
            </div>

            <div className="space-y-2 sm:space-y-3 md:space-y-4">
              <div className="flex min-h-[20px] sm:min-h-[24px] md:min-h-[24px] items-start">
                <div className="flex-shrink-0 w-4 sm:w-5 mr-1 sm:mr-1.5 pt-0.5">
                  <CheckIcon className="h-4 w-4 sm:h-5 sm:w-5 text-[#FFA6DD]" />
                </div>
                <p className="text-xs sm:text-sm font-[400] leading-[1.3] sm:leading-[1.4]">
                  {t(
                    "carCard.features.driver",
                    "Tài xế tận tâm, chuyên nghiệp"
                  )}
                </p>
              </div>
              <div className="flex min-h-[20px] sm:min-h-[24px] md:min-h-[24px] items-start">
                <div className="flex-shrink-0 w-4 sm:w-5 mr-1 sm:mr-1.5 pt-0.5">
                  <CheckIcon className="h-4 w-4 sm:h-5 sm:w-5 text-[#FFA6DD]" />
                </div>
                <p className="text-xs sm:text-sm font-[400] leading-[1.3] sm:leading-[1.4]">
                  {vehicleType === "VF8"
                    ? t("carCard.features.vf8.trunk", "Cốp xe rộng rãi")
                    : t("carCard.features.vf9.trunk", "Cốp xe siêu rộng rãi")}
                </p>
              </div>
              {vehicleType === "VF9" && (
                <div className="flex min-h-[20px] sm:min-h-[24px] md:min-h-[24px] items-start">
                  <div className="flex-shrink-0 w-4 sm:w-5 mr-1 sm:mr-1.5 pt-0.5">
                    <CheckIcon className="h-4 w-4 sm:h-5 sm:w-5 text-[#FFA6DD]" />
                  </div>
                  <p className="text-xs sm:text-sm font-[400] leading-[1.3] sm:leading-[1.4]">
                    {t(
                      "carCard.features.vf9.seats_heating",
                      "Hệ thống ghế sưởi, massage"
                    )}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Button Select Car */}
          <div className="flex justify-center mt-8">
            <Button
              className="h-12 w-full bg-transparent text-stone-950 rounded-full text-md font-[600] cursor-pointer border border-stone-950"
              onClick={onSelect}
            >
              {t("carCard.selectButton", "Chọn xe")} {model}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CarCard;
