import CarSelectionDesktop from "./CarSelectionDesktop";
import CarSelectionMobile from "./CarSelectionMobile";
import { useHourlyBookingStore } from "@/stores/useHourlyBookingStore";
import { useMediaQuery } from "@/hooks/use-media-query";

interface CarSelectionNewProps {
  onClose?: () => void;
  onSelectCar: (carType: string, sku?: string, quoteToken?: string) => void;
}

const CarSelectionNew = ({ onClose, onSelectCar }: CarSelectionNewProps) => {
  const { setSelectedVehicleModel } = useHourlyBookingStore();
  const isDesktop = useMediaQuery("(min-width: 1280px)"); // lg breakpoint

  const handleSelectCar = (carType: string, sku?: string, quoteToken?: string) => {
    // Save the selected vehicle model to the store
    setSelectedVehicleModel(carType);

    // Validate SKU and quoteToken
    if (!sku) {
      console.warn(`No SKU provided for ${carType}`);
    }
    if (!quoteToken) {
      console.warn(`No quoteToken provided for ${carType}`);
    }

    // Call the parent callback to handle car selection
    onSelectCar(carType, sku, quoteToken);
  };

  return (
    <div className={`w-full ${isDesktop ? 'bg-white' : 'bg-black!'}`}>
      {isDesktop ? (
        <CarSelectionDesktop onSelectCar={handleSelectCar} />
      ) : (
        <CarSelectionMobile
          onSelectCar={handleSelectCar}
          onClose={onClose}
          isInDialog={true}
        />
      )}
    </div>
  );
};

export default CarSelectionNew;
