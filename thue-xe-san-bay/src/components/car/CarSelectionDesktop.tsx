import { BoxIcon, CarIcon, UserIcon } from "../../assets/icons";
import { Clock, Locate, MapIcon, MapPin } from "lucide-react";
import { DialogHeader, DialogTitle } from "../ui/dialog";
import { enUS, vi } from "date-fns/locale";
import { useEffect, useState } from "react";

import { Button } from "../ui/button";
import { CheckIcon } from "lucide-react";
import { CustomImage } from "../ui/customImage";
import { format } from "date-fns";
import { useBookingModeStore } from "@/stores/useBookingModeStore";
import { useHourlyBookingStore } from "@/stores/useHourlyBookingStore";
import { useSearchStore } from "@/stores/useSearchStore";
import { useTranslation } from "react-i18next";

interface CarSelectionDesktopProps {
  onSelectCar: (carType: string, sku?: string, quoteToken?: string) => void;
}

const CarSelectionDesktop = ({ onSelectCar }: CarSelectionDesktopProps) => {
  const { t, i18n } = useTranslation();
  const {
    duration,
    getFormattedPrice,
  } = useHourlyBookingStore();
  const { mode, pricingList } = useBookingModeStore();
  const { pickup, destination, date, time } = useSearchStore();
  const isHourlyMode = mode === "hourly";

  // Function to format date based on language
  const getFormattedDate = (date: Date) => {
    const dateFormat = i18n.language === "en" ? "MM/dd/yyyy" : "dd/MM/yyyy";
    return format(date, dateFormat, {
      locale: i18n.language === "en" ? enUS : vi,
    });
  };

  // Chuyển đổi roundedMinutes thành định dạng giờ phút
  const formatTravelTime = (minutes?: number): string => {
    if (!minutes) return "1h";

    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;

    if (mins === 0) {
      return `${hours}h`;
    } else {
      return `${hours}h ${mins}m`;
    }
  };

  // State for car data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [carData, setCarData] = useState<any[]>([]);

  // Get vehicle prices based on duration or journey pricing
  useEffect(() => {
    // Sử dụng dữ liệu từ API cho cả hai chế độ
    if (pricingList && pricingList.length > 0) {
      // Map API response to car data format
      const cars = pricingList.map((item) => ({
        id: item.model === "VF8" ? 1 : 2,
        model: item.model,
        price: item.price,
        priceUSD: item.priceUSD,
        formattedPrice: getFormattedPrice(item.price, item.priceUSD),
        image: `/images/vinfast/${item.model.toLowerCase()}.png`,
        vehicleType: item.model as "VF8" | "VF9",
        duration: isHourlyMode ? duration : null,
        roundedMinutes: item.roundedMinutes,
        distance: item.distance,
        priceBlock: item.priceBlock,
        sku: item.sku,
        quoteToken: item.quoteToken,
      }));
      setCarData(cars);
    } else {
      // Không sử dụng dữ liệu giả nữa, để trống để người dùng biết cần gọi API
      setCarData([]);
    }
  }, [pricingList, isHourlyMode, duration, getFormattedPrice]);

  return (
    <>
      <DialogHeader>
        <DialogTitle className="text-xl! font-medium!">{t("tripInfo.serviceTitle", "Lựa chọn dịch vụ đưa đón sân bay GF VIP")}</DialogTitle>
      </DialogHeader>

      {/* Trip Information Display */}
      <div className="bg-gray-100 rounded-lg p-3 md:p-4 mt-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          {/* Left side - Location information */}
          <div className="flex-1">
            <div className="relative">
              {/* Vertical line - Only show if destination exists */}
              {!isHourlyMode && destination && (
                <div className="absolute left-[11px] top-6 bottom-2 w-0.5 bg-gray-300 z-0"></div>
              )}

              {/* Pickup point */}
              <div className="flex items-start mb-4 relative z-10">
                <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center bg-gray-200 rounded-full mr-3">
                  <MapPin className="h-4 w-4 text-gray-600" />
                </div>
                <div className="flex-1">
                  <p className="font-medium text-[18px] text-[#090806]">
                    {pickup}
                  </p>
                </div>
              </div>

              {/* Destination point - Only show if not hourly mode */}
              {!isHourlyMode && destination && (
                <div className="flex items-start relative z-10">
                  <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center bg-gray-200 rounded-full mr-3">
                    <Locate className="h-4 w-4 text-gray-600" />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-[18px] text-[#090806]">
                      {destination}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Right side - Trip details */}
          <div className="flex-shrink-0 space-y-2 lg:text-right">
            <div className="flex items-center lg:justify-end">
              <Clock className="w-4 h-4 mr-2 text-gray-600" />
              <span className="text-[16px] text-[#3A3938]">
                {t("tripInfo.pickupTime2", "Giờ đón")}: <span className="font-normal">{time} - {getFormattedDate(date)}</span>
              </span>
            </div>
            {!isHourlyMode && (
              <>
                <div className="flex items-center lg:justify-end">
                  <MapIcon className="w-4 h-4 mr-2 text-gray-600" />
                  <span className="text-[16px] text-[#3A3938]">
                    {t("tripInfo.distance", "Quãng đường")}: <span className="font-normal">~ {carData[0]?.distance} km</span>
                  </span>
                </div>
                <div className="flex items-center lg:justify-end">
                  <Clock className="w-4 h-4 mr-2 text-gray-600" />
                  <span className="text-[16px] text-[#3A3938]">
                    Thời gian di chuyển: <span className="font-normal">~ {formatTravelTime(carData[0]?.roundedMinutes)}</span>
                  </span>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 mt-4">
        {carData.map((car) => (
          <div
            key={car.id}
            className="bg-white rounded-2xl p-3 md:p-4 shadow-md border border-gray-200"
          >
            <div className="w-full h-full">
              {/* Information Car */}
              <div className="p-2">
                <CustomImage
                  src={car.image}
                  className="w-full h-auto object-contain mx-auto max-h-[180px]"
                />
                {/* Title and Price */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center font-[SVN-Poppins] font-medium mt-3">
                  <h3 className="text-base md:text-lg mb-2">
                    <div className="bg-black px-4 text-white text-[20px] font-[Tektur] font-bold rounded-xs">
                      {car.model === "VF8" ? "BUSINESS" : "FIRST CLASS"}
                    </div>
                  </h3>
                  <p className="text-[20px] font-medium">
                    <span>{car.formattedPrice}</span>
                  </p>
                </div>

                {/* Information Car Detail */}
                <div className="flex flex-col sm:flex-row justify-between gap-2 bg-[#E5ECEC] rounded-lg p-1.5 md:p-2.5 mt-3">
                  <div className="flex items-center gap-1 justify-center sm:justify-start">
                    <UserIcon className="w-4 h-4 sm:w-5 sm:h-5" />
                    <p className="text-base ">
                      {car.vehicleType === "VF8"
                        ? t("carCard.features.vf8.seats", "5 chỗ")
                        : t("carCard.features.vf9.seats", "6-7 chỗ")}
                    </p>
                  </div>
                  <div className="flex items-center gap-1 justify-center sm:justify-start">
                    <BoxIcon className="w-4 h-4 sm:w-5 sm:h-5" />
                    <p className="text-base">
                      {car.vehicleType === "VF8"
                        ? t("carCard.features.vf8.trunk", "Cốp xe rộng rãi")
                        : t("carCard.features.vf9.trunk", "Cốp xe siêu rộng rãi")}
                    </p>
                  </div>
                  <div className="flex items-center gap-1 justify-center sm:justify-start">
                    <CarIcon className="w-4 h-4 sm:w-5 sm:h-5" />
                    <p className="text-base ">
                      {car.vehicleType === "VF8"
                        ? t("carCard.features.vf8.type", "D-SUV")
                        : t("carCard.features.vf9.type", "E-SUV")}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-4 mt-4">
                  <div className="space-y-1.5 sm:space-y-2 md:space-y-3">
                    <div className="flex min-h-[20px] items-start">
                      <div className="flex-shrink-0 w-4 sm:w-5 mr-1 sm:mr-1.5 pt-0.5">
                        <CheckIcon className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-[#07F668]" />
                      </div>
                      <p className="text-[14px]  leading-tight">
                        {car.vehicleType === "VF8"
                          ? t("carCard.features.vf8.seats", "5 chỗ")
                          : t("carCard.features.vf9.seats", "6-7 chỗ")}
                      </p>
                    </div>
                    <div className="flex min-h-[20px] items-start">
                      <div className="flex-shrink-0 w-4 sm:w-5 mr-1 sm:mr-1.5 pt-0.5">
                        <CheckIcon className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-[#07F668]" />
                      </div>
                      <p className="text-[14px]  leading-tight">
                        {t(
                          "carCard.features.airbags",
                          "Hệ thống bảo vệ 11 túi khí"
                        )}
                      </p>
                    </div>
                    <div className="flex min-h-[20px] items-start">
                      <div className="flex-shrink-0 w-4 sm:w-5 mr-1 sm:mr-1.5 pt-0.5">
                        <CheckIcon className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-[#07F668]" />
                      </div>
                      <p className="text-[14px]  leading-tight">
                        {t(
                          "carCard.features.charging",
                          "Sạc điện thoại miễn phí"
                        )}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-1.5 sm:space-y-2 md:space-y-3">
                    <div className="flex min-h-[20px] items-start">
                      <div className="flex-shrink-0 w-4 sm:w-5 mr-1 sm:mr-1.5 pt-0.5">
                        <CheckIcon className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-[#07F668]" />
                      </div>
                      <p className="text-[14px] leading-tight">
                        {t(
                          "carCard.features.driver",
                          "Tài xế tận tâm, chuyên nghiệp"
                        )}
                      </p>
                    </div>
                    <div className="flex min-h-[20px] items-start">
                      <div className="flex-shrink-0 w-4 sm:w-5 mr-1 sm:mr-1.5 pt-0.5">
                        <CheckIcon className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-[#07F668]" />
                      </div>
                      <p className="text-[14px] leading-tight">
                        {car.vehicleType === "VF8"
                          ? t("carCard.features.vf8.trunk", "Cốp xe rộng rãi")
                          : t(
                            "carCard.features.vf9.trunk",
                            "Cốp xe siêu rộng rãi"
                          )}
                      </p>
                    </div>
                    {car.vehicleType === "VF9" && (
                      <div className="flex min-h-[20px] items-start">
                        <div className="flex-shrink-0 w-4 sm:w-5 mr-1 sm:mr-1.5 pt-0.5">
                          <CheckIcon className="h-3.5 w-3.5 sm:h-4 sm:w-4 text-[#07F668]" />
                        </div>
                        <p className="text-[14px] leading-tight">
                          {t(
                            "carCard.features.vf9.seats_heating",
                            "Hệ thống ghế sưởi, massage"
                          )}
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Button Select Car */}
                <div className="flex justify-center mt-5 md:mt-6">
                  <Button
                    className="h-10 sm:h-12 w-full bg-transparent text-stone-950 rounded-full text-sm sm:text-md font-[600] cursor-pointer border border-stone-950 hover:bg-gray-100 transition-colors"
                    onClick={() => onSelectCar(car.vehicleType, car.sku, car.quoteToken)}
                  >
                    {t("carCard.selectButtonWithModel", "Chọn dịch vụ")}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </>
  );
};

export default CarSelectionDesktop;
