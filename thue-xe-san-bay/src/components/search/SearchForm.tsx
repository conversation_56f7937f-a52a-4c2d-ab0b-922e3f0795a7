import React, { useEffect, useCallback } from "react";
import { type SearchFormValues } from "../../lib/validations";
import { useSearchStore, useSuccessStore, useWarningStore } from "../../stores";
import { useTranslation } from "react-i18next";
import BookingFormByRoute from "../booking/BookingFormByRoute";
import { useLocationStore, useLoadingStore } from "../../stores";
import { useBookingModeStore } from "../../stores/useBookingModeStore";
import CarSelectionNew from "../car/CarSelectionNew";
import BookingModal from "../booking/BookingModal";
import MobileTestModal from "../test/MobileTestModal";
import { Dialog, DialogContent } from "../ui/dialog";
import DialogWrapper from "../ui/dialog-wrapper";
import FormSuccess from "../notification/FormSuccess";
import FormWarning from "../notification/FormWarning";
import { calculateAirportBooking } from "@/api/services/location";
import { useGoogleReCaptcha } from "react-google-recaptcha-v3";
import { useMediaQuery } from "@/hooks/use-media-query";

const SearchForm: React.FC = () => {
  const { t } = useTranslation();
  const loadingStore = useLoadingStore();
  const { executeRecaptcha } = useGoogleReCaptcha();

  const { setLoading } = loadingStore;
  // Use Zustand stores
  const { pickup, destination, date, time, fetchAirportOptions } =
    useSearchStore();

  const { setPricingList } = useBookingModeStore();
  const { isSuccess, setIsSuccess } = useSuccessStore();
  const { isWarning, setIsWarning } = useWarningStore();
  const [showCarSelection, setShowCarSelection] = React.useState(false);
  const [showBookingModal, setShowBookingModal] = React.useState(false);
  const [selectedCarData, setSelectedCarData] = React.useState<{
    carType: string;
    sku?: string;
    quoteToken?: string;
  } | null>(null);

  const isDesktop = useMediaQuery("(min-width: 1024px)");

  // Gọi API để lấy danh sách sân bay khi component được mount
  useEffect(() => {
    fetchAirportOptions();
  }, [fetchAirportOptions]);

  const handleRouteSubmit = async (data: SearchFormValues) => {
    if (!executeRecaptcha) {
      console.error("reCAPTCHA not available");
      return;
    }

    setLoading(true, t("loading.searchingVehicles", "Đang tìm xe phù hợp..."));

    try {
      const { origin, destination } = useLocationStore.getState();

      // Kiểm tra origin và destination có tồn tại không
      if (!origin || !destination) {
        console.error("Origin or destination is missing");
        setLoading(false);
        return;
      }

      // Không cần gọi API tính thời gian di chuyển từ getTravelTimeOSM nữa

      // Tạo một bản sao của ngày đã chọn để tránh ảnh hưởng đến state
      const selectedDate = new Date(data.date);
      // Format ngày theo định dạng YYYY-MM-DD mà không chuyển đổi sang UTC
      const formattedDate = `${selectedDate.getFullYear()}-${String(
        selectedDate.getMonth() + 1
      ).padStart(2, "0")}-${String(selectedDate.getDate()).padStart(2, "0")}`;

      // Đảm bảo latitude và longitude không phải là null hoặc "null"
      const originLat = origin.latitude && origin.latitude !== "null" ? origin.latitude : "";
      const originLon = origin.longitude && origin.longitude !== "null" ? origin.longitude : "";
      const destLat = destination.latitude && destination.latitude !== "null" ? destination.latitude : "";
      const destLon = destination.longitude && destination.longitude !== "null" ? destination.longitude : "";

      // Lấy token reCAPTCHA
      const captchaToken = await executeRecaptcha("calculateBooking");

      const payload = {
        origin: {
          address: origin.address || "",
          latitude: originLat,
          longitude: originLon,
          placeId: origin.placeId || "",
        },
        destination: {
          address: destination.address || "",
          latitude: destLat,
          longitude: destLon,
          placeId: destination.placeId || "",
        },
        model: ["VF8", "VF9"], // <-- lấy từ form user chọn
        pickupDate: formattedDate, // YYYY-MM-DD không bị ảnh hưởng bởi múi giờ
        // Không cần thêm durationInMinutes vào payload nữa
      };

      const response = await calculateAirportBooking(payload, captchaToken); // <-- Gọi hàm đã tách với captchaToken

      // Kiểm tra lỗi từ API
      if (response.error) {
        console.error("API error:", response.error);

        // Kiểm tra nếu là lỗi DISTANCE_OUT_OF_RANGE_ERROR
        if (response.error && response.error.includes("DISTANCE_OUT_OF_RANGE_ERROR")) {
          setIsWarning(true);
          return;
        }

        // Xử lý các lỗi khác nếu cần
        return;
      }

      setPricingList(response.data || []); // <-- Lưu vào store
      setShowCarSelection(true);
    } catch (error) {
      console.error("Unexpected error during booking:", error);
      // TODO: Optionally show toast error
    } finally {
      setLoading(false);
    }
  };

  // Handle car selection from CarSelectionNew
  const handleCarSelection = (carType: string, sku?: string, quoteToken?: string) => {
    setSelectedCarData({ carType, sku, quoteToken });
    setShowCarSelection(false); // Close car selection modal
    setShowBookingModal(true); // Open booking modal
  };

  // Handle booking success
  const handleBookingSuccess = () => {
    setShowBookingModal(false);
    setSelectedCarData(null);
    setIsSuccess(true);
  };

  // Khởi tạo reCAPTCHA khi component được mount
  const handleReCaptchaVerify = useCallback(async () => {
    if (executeRecaptcha) {
      // Khởi tạo reCAPTCHA
      await executeRecaptcha("init");
    }
  }, [executeRecaptcha]);

  useEffect(() => {
    handleReCaptchaVerify();
  }, [handleReCaptchaVerify]);

  return (
    <div
      id="search-form"
      className="w-full max-w-[90wh] rounded-4xl shadow-lg mt-auto -mb-16 p-4 md:p-0"
    >
      <BookingFormByRoute
        onSubmit={handleRouteSubmit}
        initialValues={{
          pickup,
          destination,
          date,
          time,
        }}
      />

      {/* Dialog for car selection */}
      {isDesktop ? (
        <DialogWrapper
          isOpen={showCarSelection}
          onClose={() => setShowCarSelection(false)}
          mobileFullHeight={true}
          backgroundClass="bg-white"
          maxWidth="1200px"
        >
          <CarSelectionNew
            onClose={() => setShowCarSelection(false)}
            onSelectCar={handleCarSelection}
          />
        </DialogWrapper>
      ) : (
        <MobileTestModal
          isOpen={showCarSelection}
          onClose={() => setShowCarSelection(false)}
        />
      )}

      {/* Booking Modal */}
      {selectedCarData && (
        <BookingModal
          isOpen={showBookingModal}
          onClose={() => {
            setShowBookingModal(false);
            setSelectedCarData(null);
          }}
          selectedCar={selectedCarData.carType}
          selectedSku={selectedCarData.sku}
          selectedQuoteToken={selectedCarData.quoteToken}
          onSuccess={handleBookingSuccess}
        />
      )}

      {/* Success dialog - hiển thị riêng biệt, không phụ thuộc vào dialog car selection */}
      <FormSuccess isOpen={isSuccess} onClose={() => setIsSuccess(false)} />

      {/* Warning dialog - hiển thị khi có lỗi DISTANCE_OUT_OF_RANGE_ERROR */}
      <FormWarning isOpen={isWarning} onClose={() => setIsWarning(false)} />
    </div>
  );
};

export default SearchForm;
