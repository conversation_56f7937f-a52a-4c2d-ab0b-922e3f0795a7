import { Control, Controller, FieldErrors } from "react-hook-form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";

import React from "react";
import { SearchFormValues } from "../../lib/validations";
import { useSearchStore } from "../../stores";
import { LocationItem as SearchLocationItem } from "../search/LocationInput";
import { useTranslation } from "react-i18next";

interface LocationSelectorProps {
  name: "pickup" | "destination";
  label: string;
  control: Control<SearchFormValues>;
  errors: FieldErrors<SearchFormValues>;
}

const LocationSelector: React.FC<LocationSelectorProps> = ({
  name,
  label,
  control,
  errors,
}) => {
  const { i18n, t } = useTranslation();
  const {
    setPickup,
    setDestination,
    availablePickupOptions,
    availableDestinationOptions,
  } = useSearchStore();

  const handleValueChange = (value: string) => {
    // Find the selected option
    const options = name === "pickup"
      ? availablePickupOptions()
      : availableDestinationOptions();

    const selectedOption = options.find(option => option.code === value);

    if (selectedOption) {
      const locationItem: SearchLocationItem = {
        name: selectedOption.name,
        code: selectedOption.code,
        placeId: selectedOption.code, // Sử dụng code làm place_id cho các sân bay
        address: selectedOption.address || selectedOption.name,
        longitude: selectedOption.longitude === null ? "" : String(selectedOption.longitude || ""),
        latitude: selectedOption.latitude === null ? "" : String(selectedOption.latitude || ""),
        id: selectedOption.id // Thêm id từ selectedOption
      };

      if (name === "pickup") {
        setPickup(locationItem);
      } else {
        setDestination(locationItem);
      }
    }
  };

  // Get options
  const options = name === "pickup"
    ? availablePickupOptions()
    : availableDestinationOptions();

  return (
    <div className="w-full md:w-1/4 p-2.5">
      <label className="block text-sm font-medium text-gray-700 py-1 mb-1">
        {label} <span className="text-red-500">*</span>
      </label>
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <Select
            onValueChange={(value) => {
              handleValueChange(value);
              field.onChange(value);
            }}
            value={field.value}
          >
            <SelectTrigger
              className={`w-full h-12 bg-white ${
                errors[name] ? "border-red-500 ring-1 ring-red-500" : ""
              }`}
            >
              <SelectValue
                placeholder={t(
                  "searchForm.selectLocation",
                  `Chọn ${label.toLowerCase()}`
                )}
              />
            </SelectTrigger>
            <SelectContent updatePositionStrategy="optimized">
              {Array.isArray(options) && options.length > 0 ? (
                options.map((location) => (
                  <SelectItem key={location.code} value={location.code}>
                    {i18n.language === "en" ? location.address : location.name}
                  </SelectItem>
                ))
              ) : (
                <SelectItem value="no-locations" disabled>
                  {t("searchForm.noLocations", "Không có địa điểm")}
                </SelectItem>
              )}
            </SelectContent>
          </Select>
        )}
      />
    </div>
  );
};

export default LocationSelector;
