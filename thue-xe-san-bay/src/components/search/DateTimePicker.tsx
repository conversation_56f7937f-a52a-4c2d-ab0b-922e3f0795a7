import React from "react";
import { Control, FieldErrors } from "react-hook-form";
import { SearchFormValues } from "../../lib/validations";
import DateTimePickerBase from "./DateTimePickerBase";

interface DateTimePickerProps {
  dateControl: Control<SearchFormValues>;
  timeControl: Control<SearchFormValues>;
  errors: FieldErrors<SearchFormValues>;
}

const DateTimePicker: React.FC<DateTimePickerProps> = ({
  dateControl,
  timeControl,
  errors,
}) => {
  return (
    <DateTimePickerBase
      dateControl={dateControl}
      timeControl={timeControl}
      errors={errors}
    />
  );
};

export default DateTimePicker;
