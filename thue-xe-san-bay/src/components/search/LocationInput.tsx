import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";

import { AutocompletePrediction } from "@/api/services/location";
// Sử dụng interface từ API service
import { Input } from "../ui/input";
import { locationService } from "@/api";
import { useLocationStore } from "@/stores/useLocationStore";
import { useSearchStore, AirportOption } from "@/stores";

export interface LocationItem {
  code: string;
  name: string;
  placeId: string;
  address: string;
  longitude: string;
  latitude: string;
  id?: number; // Add optional id property for airport locations
}

interface LocationInputProps {
  value: string;
  onChange: (value: string, location?: LocationItem) => void;
  placeholder?: string;
  error?: string;
  className?: string;
  isOrigin?: boolean;
  isPickup?: boolean;
}

const LocationInput: React.FC<LocationInputProps> = ({
  value,
  onChange,
  placeholder,
  error,
  className,
  isOrigin = false,
  isPickup = false,
}) => {
  const { t, i18n } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [searchText, setSearchText] = useState(value);
  const [dropdownPosition, setDropdownPosition] = useState<"bottom" | "top">(
    "bottom"
  );
  const [shouldAlignLeft, setShouldAlignLeft] = useState(false);
  const [searchResults, setSearchResults] = useState<AutocompletePrediction[]>(
    []
  );
  const [isLoading, setIsLoading] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const {
    availablePickupOptions,
    availableDestinationOptions,
    shouldShowOnlyAirports,
  } = useSearchStore();
  const { setOrigin, setDestination } = useLocationStore();
  const airportOptions = isPickup
    ? availablePickupOptions()
    : availableDestinationOptions();
  const showOnlyAirports = shouldShowOnlyAirports(isPickup);

  useEffect(() => {
    setSearchText(value);
  }, [value]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    const handleScroll = () => {
      if (!containerRef.current || !isOpen) return;

      const rect = containerRef.current.getBoundingClientRect();
      const spaceBelow = window.innerHeight - rect.bottom;
      const spaceAbove = rect.top;
      const dropdownHeight = 200;

      setDropdownPosition(
        spaceBelow >= dropdownHeight || spaceBelow >= spaceAbove
          ? "bottom"
          : "top"
      );
    };

    document.addEventListener("mousedown", handleClickOutside);
    document.addEventListener("scroll", handleScroll, true);
    window.addEventListener("resize", handleScroll);

    handleScroll();

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("scroll", handleScroll, true);
      window.removeEventListener("resize", handleScroll);
    };
  }, [isOpen]);

  useEffect(() => {
    const handlePosition = () => {
      if (!containerRef.current || !isOpen) return;

      const rect = containerRef.current.getBoundingClientRect();
      const spaceBelow = window.innerHeight - rect.bottom;
      const spaceAbove = rect.top;
      const dropdownHeight = 200;

      // Update breakpoint to include iPad sizes (up to 1024px)
      const isTabletOrMobile = window.innerWidth < 1024; // lg breakpoint in Tailwind

      // Vertical position
      setDropdownPosition(
        spaceBelow >= dropdownHeight || spaceBelow >= spaceAbove
          ? "bottom"
          : "top"
      );

      // Horizontal alignment - improved for tablet devices
      if (isTabletOrMobile) {
        // On mobile and tablet devices, always align left to avoid overflow
        setShouldAlignLeft(false);
      } else {
        // On desktop, calculate based on available space
        const viewportWidth = window.innerWidth;
        const elementRight = rect.right;
        const expandedWidth = rect.width * 1.5; // 150% of original width

        // If there's not enough space on the right, align to the left
        setShouldAlignLeft(elementRight + expandedWidth > viewportWidth);
      }
    };

    handlePosition();
    window.addEventListener("resize", handlePosition);
    window.addEventListener("scroll", handlePosition, true);

    return () => {
      window.removeEventListener("resize", handlePosition);
      window.removeEventListener("scroll", handlePosition, true);
    };
  }, [isOpen]);

  useEffect(() => {
    const searchLocations = async () => {
      if (searchText.length < 2 || showOnlyAirports) {
        setSearchResults([]);
        return;
      }

      // Kiểm tra nếu dropdown đã đóng thì không gọi API
      if (isOpen === false) {
        return;
      }

      setIsLoading(true);
      try {
        // Sử dụng API mới từ backend thay vì OpenStreetMap
        // Truyền ngôn ngữ hiện tại vào API
        const response = await locationService.getLocationSuggestions(
          searchText,
          i18n.language // Truyền ngôn ngữ hiện tại (vi hoặc en)
        );

        if (response.data) {
          setSearchResults(response.data);
        } else {
          setSearchResults([]);
        }
      } catch (error) {
        console.error("Error searching locations:", error);
        setSearchResults([]);
      } finally {
        setIsLoading(false);
      }
    };

    const timeoutId = setTimeout(searchLocations, 500);
    return () => clearTimeout(timeoutId);
  }, [searchText, showOnlyAirports, isOpen, i18n.language]);

  const handleLocationSelect = (option: AutocompletePrediction | AirportOption) => {
    // Kiểm tra xem option có phải từ danh sách sân bay có sẵn không
    const isAirportOption = "code" in option && !("place_id" in option);

    // Đóng dropdown trước khi cập nhật searchText để tránh gọi API lại
    setIsOpen(false);
    // Cập nhật searchText ngay lập tức
    if ("code" in option) {
      const option_safe = option as Record<string, unknown>;
      setSearchText(i18n.language === "en" ? (option_safe.address as string) : (option_safe.name as string));
    } else {
      const autocompleteOption = option as AutocompletePrediction;
      setSearchText(autocompleteOption.address);
    }

    if (isAirportOption) {
      // Tạo LocationItem đầy đủ từ thông tin sẵn có trong airportOptions
      const option_safe = option as Record<string, unknown>;
      const locationItem: LocationItem = {
        code: "code" in option ? option.code : "",
        name: option_safe.name as string,
        placeId: "code" in option ? option.code : "",
        latitude: "latitude" in option && option.latitude === null ? "" : String("latitude" in option ? option.latitude : ("lat" in option_safe ? option_safe.lat : "")),
        longitude: "longitude" in option && option.longitude === null ? "" : String("longitude" in option ? option.longitude : ("lon" in option_safe ? option_safe.lon : "")),
        address: "address" in option && option.address ? option.address : option_safe.name as string,
        id: "id" in option ? (option.id as number) : undefined // Include id if available
      };

      // Cập nhật store với thông tin đầy đủ
      if (isOrigin) {
        setOrigin(locationItem);
      } else {
        setDestination(locationItem);
      }

      // Gọi onChange để cập nhật form
      onChange(option_safe.address as string, locationItem);
    } else {
      // Chuyển đổi option thành LocationItem
      const option_safe = option as AutocompletePrediction;
      const locationItem: LocationItem = {
        code: "",
        name: option_safe.name,
        placeId: option_safe.placeId,
        latitude: option_safe.latitude === null ? "" : String(option_safe.latitude || ""), // Chuyển null thành chuỗi rỗng
        longitude: option_safe.longitude === null ? "" : String(option_safe.longitude || ""), // Chuyển null thành chuỗi rỗng
        address: option_safe.address
      };

      if (isOrigin) {
        setOrigin(locationItem);
      } else {
        setDestination(locationItem);
      }

      onChange(option_safe.address, locationItem);
    }
  };

  // Xác định kiểu dữ liệu rõ ràng cho allOptions
  const allOptions: (AutocompletePrediction | AirportOption)[] =
    searchText.length < 2 || showOnlyAirports ? airportOptions : searchResults;

  return (
    <div className="relative" ref={containerRef}>
      <Input
        type="text"
        value={searchText}
        onChange={(e) => {
          const newValue = e.target.value;
          setSearchText(newValue);
          // Pass undefined as location to indicate user is typing, not selecting
          onChange(newValue, undefined);
          setIsOpen(true);
        }}
        onFocus={() => setIsOpen(true)}
        placeholder={placeholder}
        className={className}
        readOnly={showOnlyAirports}
      />

      {isOpen && (
        <div
          className={`absolute z-20 w-full lg:w-[150%] bg-white border border-gray-200 rounded-md shadow-lg ${
            dropdownPosition === "bottom" ? "mt-1" : "bottom-[calc(100%+4px)]"
          } ${shouldAlignLeft ? "right-0" : "left-0"}`}
          style={{
            maxHeight: "200px",
            overflowY: "auto",
          }}
        >
          <div className="py-1">
            {isLoading ? (
              <div className="px-4 py-2 text-gray-500">{t("searchForm.searching", "Đang tìm kiếm...")}</div>
            ) : allOptions.length === 0 ? (
              <div className="px-4 py-2 text-gray-500">
                {t("searchForm.enterLocation", "Vui lòng nhập địa điểm")}
              </div>
            ) : (
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              allOptions.map((option: any) => (
                <div
                  key={"code" in option ? option.code : ("placeId" in option ? option.placeId : "")}
                  className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                  onClick={() => handleLocationSelect(option)}
                >
                  <div className="font-medium">
                    {"code" in option ? (i18n.language === "en" ? option.address : option.name) : ("address" in option ? option.address : "")}
                  </div>
                  {"description" in option && (
                    <div className="text-xs text-gray-500 mt-0.5 truncate">
                      {option.address}
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
    </div>
  );
};

export default LocationInput;
