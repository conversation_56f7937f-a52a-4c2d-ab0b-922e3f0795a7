import { useEffect, useState } from "react";
import {
  Control,
  Controller,
  FieldErrors,
  useWatch,
  useFormContext,
  Path,
  PathValue,
} from "react-hook-form";
import { Button } from "../ui/button";
import { EnhancedCalendar } from "../ui/enhanced-calendar";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectValue,
} from "../ui/select";
import { CustomSelectTrigger } from "../ui/custom-select-trigger";
import { useSearchStore } from "../../stores";
import { useVehicleStore } from "../../stores";
import { useHolidayStore } from "../../stores/holiday.store";
import { CalendarIcon, ClockIcon } from "../../assets/icons";
import { format, isToday } from "date-fns";
import { vi, enUS } from "date-fns/locale";
import { cn } from "../../lib/utils";
import { useMediaQuery } from "../../hooks/use-media-query";
import { useTranslation } from "react-i18next";
import FormLabel from "../ui/form-label";
import {
  getAvailableTimes,
  getFirstAvailableTime,
  getFromDate,
  getTomorrow,
  hasTodayAvailableTime,
  isTimeAllowed,
} from "../../utils/time-utils";

// Generic type for form values
interface DateTimeFormValues {
  date: Date;
  time: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

interface DateTimePickerBaseProps<T extends DateTimeFormValues> {
  dateControl: Control<T>;
  timeControl: Control<T>;
  errors: FieldErrors<T>;
}

function DateTimePickerBase<T extends DateTimeFormValues>({
  dateControl,
  timeControl,
  errors,
}: DateTimePickerBaseProps<T>) {
  const { setDate, setTime } = useSearchStore();
  const { setSelectedDate } = useVehicleStore();
  const { fetchHolidays, isHolidayDate } = useHolidayStore();
  const isMobile = useMediaQuery("(max-width: 768px)");
  const { t, i18n } = useTranslation();
  const formContext = useFormContext<T>();
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());

  useEffect(() => {
    fetchHolidays();
  }, [fetchHolidays]);

  // Watch date field changes
  const selectedDate = useWatch({
    control: dateControl,
    name: "date" as Path<T>,
  }) as Date | undefined;

  // Watch time field changes - used in the UI rendering
  useWatch({
    control: timeControl,
    name: "time" as Path<T>,
  });

  // Chọn locale dựa vào ngôn ngữ hiện tại
  const dateLocale = i18n.language === "en" ? enUS : vi;

  // Lấy các giá trị từ time-utils
  const fromDate = getFromDate();
  const tomorrow = getTomorrow();
  const availableTimes = getAvailableTimes();
  const hasAvailableTimeToday = hasTodayAvailableTime();

  // Kiểm tra và cập nhật giờ khi ngày thay đổi
  useEffect(() => {
    if (selectedDate) {
      // Khi ngày thay đổi, luôn cập nhật giờ mặc định dựa vào ngày mới
      const newTime = getFirstAvailableTime(selectedDate);

      setTime(newTime);
      if (formContext) {
        formContext.setValue("time" as Path<T>, newTime as PathValue<T, Path<T>>);
      }
    }
  }, [selectedDate, setTime, formContext]);

  // Đảm bảo time luôn có giá trị hợp lệ
  useEffect(() => {
    const currentTime = formContext?.getValues("time" as Path<T>) as string | undefined;

    // Nếu time không có giá trị hoặc không hợp lệ, thiết lập lại giá trị mặc định
    if (!currentTime || !currentTime.match(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)) {
      const defaultTime = getFirstAvailableTime(selectedDate || new Date());
      setTime(defaultTime);
      if (formContext) {
        formContext.setValue("time" as Path<T>, defaultTime as PathValue<T, Path<T>>);
      }
    }
  }, [formContext, selectedDate, setTime]);

  // Cập nhật useEffect để thiết lập ngày mặc định khi component mount
  useEffect(() => {
    // Nếu ngày hôm nay còn giờ có thể chọn, set ngày mặc định là ngày hôm nay
    if (hasAvailableTimeToday) {
      const today = new Date();
      setDate(today);
      setSelectedDate(today);
      if (formContext) {
        formContext.setValue("date" as Path<T>, today as PathValue<T, Path<T>>);
      }
    }
    // Nếu ngày hôm nay không còn giờ nào có thể chọn, set ngày mặc định là ngày mai
    else {
      setDate(tomorrow);
      setSelectedDate(tomorrow);
      if (formContext) {
        formContext.setValue("date" as Path<T>, tomorrow as PathValue<T, Path<T>>);
      }
    }

    // Cập nhật thời gian mặc định dựa vào ngày được chọn
    setTimeout(() => {
      if (formContext?.getValues("date" as Path<T>)) {
        const defaultTime = getFirstAvailableTime(
          formContext.getValues("date" as Path<T>) as Date
        );
        setTime(defaultTime);
        if (formContext) {
          formContext.setValue("time" as Path<T>, defaultTime as PathValue<T, Path<T>>);
        }
      }
    }, 0);
  }, [selectedDate, setSelectedDate]);

  // Sync date with VehicleStore
  useEffect(() => {
    if (selectedDate) {
      setSelectedDate(selectedDate as Date);
    }
  }, [selectedDate, setSelectedDate]);

  return (
    <div className="w-full lg:w-1/4 p-2">
      <FormLabel
        text={t("dateTimePicker.selectDate", "Chọn ngày đi")}
        required={true}
      />
      <div className="flex shadow-sm rounded-md overflow-hidden">
        <div className="flex-1 relative">
          <Controller
            name={"date" as Path<T>}
            control={dateControl}
            render={({ field }) => (
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full h-12 justify-between font-normal bg-white rounded-r-none hover:bg-gray-50",
                      !field.value && "text-muted-foreground",
                      errors.date && "border-red-500 ring-1 ring-red-500"
                    )}
                  >
                    {field.value
                      ? format(
                          field.value,
                          i18n.language === "en" ? "MM/dd/yyyy" : "dd/MM/yyyy",
                          {
                            locale: dateLocale,
                          }
                        )
                      : t(
                          "dateTimePicker.dateFormat",
                          i18n.language === "en" ? "MM/DD/YYYY" : "DD/MM/YYYY"
                        )}
                    <CalendarIcon />
                  </Button>
                </PopoverTrigger>
                <PopoverContent
                  className="w-[calc(100vw-32px)] p-0 sm:w-[300px] md:w-auto md:min-w-[48rem] md:max-w-[50rem] rounded-xl shadow-lg border-gray-200"
                  align={isMobile ? "center" : "start"}
                  side={isMobile ? "bottom" : undefined}
                  sideOffset={isMobile ? 8 : 4}
                >
                  <div className="px-1 py-2">
                    <EnhancedCalendar
                      locale={dateLocale}
                      mode="single"
                      selected={field.value}
                      defaultMonth={currentMonth}
                      onMonthChange={setCurrentMonth}
                      onSelect={(newDate) => {
                        if (newDate) {
                          setDate(newDate);
                          setSelectedDate(newDate);
                          field.onChange(newDate);
                        }
                        document
                          .querySelector("[data-state='open']")
                          ?.dispatchEvent(
                            new KeyboardEvent("keydown", {
                              key: "Escape",
                            })
                          );
                      }}
                      initialFocus
                      numberOfMonths={isMobile ? 1 : 2}
                      className="p-4 w-full"
                      fromDate={fromDate}
                      disabled={(date) => {
                        // Disable today if there are no valid time slots available
                        if (isToday(date)) {
                          return !hasAvailableTimeToday;
                        }
                        return false;
                      }}
                      modifiers={{
                        holiday: (date) => isHolidayDate(date) !== undefined,
                      }}
                      modifiersClassNames={{
                        holiday: "bg-red-50 text-red-600 font-medium rounded-full",
                        selected: "!bg-[#07F668] text-black hover:bg-[#00C853] rounded-full",
                        "selected.holiday": "bg-[#FF5252] text-white hover:bg-[#E53935] focus:bg-[#E53935] rounded-full shadow-sm",
                      }}
                    />

                    <div className="px-3 sm:px-6 py-3 sm:py-4 border-t border-gray-100 mt-2">
                      <div className="flex flex-col sm:flex-row flex-wrap items-start sm:items-center gap-2 sm:gap-4">
                        <div className="flex items-center gap-2">
                          <div className="w-4 sm:w-5 h-4 sm:h-5 bg-[#07F668] rounded-full shadow-sm"></div>
                          <span className="text-xs sm:text-sm text-gray-700 font-medium">{t("dateTimePicker.selectedDay", "Ngày đã chọn")}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-4 sm:w-5 h-4 sm:h-5 bg-red-50 rounded-full shadow-sm flex items-center justify-center">
                            <div className="w-2 sm:w-2.5 h-2 sm:h-2.5 bg-red-500 rounded-full"></div>
                          </div>
                          <span className="text-xs sm:text-sm text-gray-700 font-medium">{t("dateTimePicker.holidaySurcharge", "Ngày lễ, giá có thêm phụ phí")}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-4 sm:w-5 h-4 sm:h-5 bg-[#07F668] rounded-full shadow-sm flex items-center justify-center">
                            <div className="w-2 sm:w-2.5 h-2 sm:h-2.5 bg-red-500 rounded-full"></div>
                          </div>
                          <span className="text-xs sm:text-sm text-gray-700 font-medium">{t("dateTimePicker.holidaySelected", "Ngày lễ đã chọn")}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            )}
          />
        </div>

        {/* Input giờ */}
        <div className="w-1/3">
          <Controller
            name={"time" as Path<T>}
            control={timeControl}
            rules={{ required: true }}
            render={({ field }) => {
              // Đảm bảo field.value luôn có giá trị hợp lệ
              if (!field.value || !field.value.match(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)) {
                const defaultTime = getFirstAvailableTime(selectedDate || new Date());
                setTimeout(() => {
                  field.onChange(defaultTime);
                  setTime(defaultTime);
                }, 0);
              }

              return (
                <Select
                  onValueChange={(value) => {
                    setTime(value);
                    field.onChange(value);
                  }}
                  value={field.value}
                >

                <CustomSelectTrigger
                  className={`w-full h-12 bg-white rounded-l-none focus:ring-offset-0 hover:bg-gray-50 border-l-0 ${
                    errors.time ? "border-red-500 ring-1 ring-red-500" : ""
                  }`}
                >
                  <div className="flex items-center justify-between w-full">
                    <SelectValue placeholder={t("dateTimePicker.time", "Giờ")} />
                    <ClockIcon className="ml-2 opacity-70" />
                  </div>
                </CustomSelectTrigger>
                <SelectContent className="max-h-[300px] overflow-y-auto rounded-xl shadow-lg border-gray-200 p-2">
                  {availableTimes.map((timeValue) => {
                    const allowed = isTimeAllowed(timeValue, selectedDate as Date | null);

                    return allowed ? (
                      <SelectItem
                        key={timeValue}
                        value={timeValue}
                        className="hover:bg-gray-50 cursor-pointer py-2 my-1 rounded-md"
                      >
                        <div className="flex items-center justify-center w-full">
                          <span>{timeValue}</span>
                        </div>
                      </SelectItem>
                    ) : null;
                  })}
                </SelectContent>
              </Select>
              );
            }}
          />
        </div>
      </div>
    </div>
  );
}

export default DateTimePickerBase;
