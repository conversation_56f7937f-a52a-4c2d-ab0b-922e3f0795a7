import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { useTranslation } from "react-i18next";

interface FormWarningProps {
  isOpen: boolean;
  onClose: () => void;
}

const FormWarning = ({ isOpen, onClose }: FormWarningProps) => {
  const { t } = useTranslation();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg flex flex-col items-center text-center p-6">
        <img
          src={`${import.meta.env.VITE_BASE_PATH}/images/banner/distance-130.png`}
          alt={t("formWarning.imageAlt", "Distance Warning")}
          className="h-32 w-auto mb-2"
        />

        <h2 className="text-[24px] font-medium text-[#E53935] mb-4">
          {t("formWarning.title")}
        </h2>

        <p className="text-[#3A3938] mb-4 text-base">
          {t("formWarning.message")}
        </p>

        <p className="text-[#3A3938] mb-6 text-md font-medium">
          {t("formWarning.contactText")}{" "}
          <a href={`tel:${t("formWarning.contactNumber")}`} className="text-primary hover:underline">
            {t("formWarning.contactNumber")}
          </a>
        </p>

        <Button
          onClick={onClose}
          className="bg-black text-white hover:bg-[#090806] w-full py-6 rounded-full cursor-pointer"
        >
          {t("formWarning.closeButton")}
        </Button>

        <Button
          onClick={() => window.location.href = `tel:${t("formWarning.contactNumber")}`}
          className="bg-white text-black border border-black hover:bg-gray-100 w-full py-6 rounded-full cursor-pointer"
        >
          {t("formWarning.contactButton")}
        </Button>
      </DialogContent>
    </Dialog>
  );
};

export default FormWarning;
