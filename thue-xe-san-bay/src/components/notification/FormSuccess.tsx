import { Button } from "@/components/ui/button";
import { CheckCircle2 } from "lucide-react";
import DialogWrapper from "@/components/ui/dialog-wrapper";
import { useTranslation } from "react-i18next";

interface FormSuccessProps {
  isOpen: boolean;
  onClose: () => void;
}

const FormSuccess = ({ isOpen, onClose }: FormSuccessProps) => {
  const { t } = useTranslation();

  return (
    <DialogWrapper
      isOpen={isOpen}
      onClose={onClose}
      maxWidth="lg"
      padding="p-6"
    >
      <div className="flex flex-col items-center text-center">
        <CheckCircle2
          className="h-24 w-24 text-[#1AB759] mb-2"
          strokeWidth={1.5}
        />

        <h2 className="text-[24px] font-medium text-[#1AB759] mb-4">
          {t("formSuccess.title", "Chúng tôi đã nhận thông tin")}
        </h2>

        <p className="text-[#3A3938] text-md">{t("formSuccess.message")}</p>

        <p className="text-[#3A3938] mb-4 text-md">
          {t("formSuccess.message2")}
        </p>

        <Button
          onClick={onClose}
          className="bg-primary mb-4 text-black w-full py-6 rounded-full cursor-pointer"
        >
          {t("formSuccess.closeButton")}
        </Button>
      </div>
    </DialogWrapper>
  );
};

export default FormSuccess;
