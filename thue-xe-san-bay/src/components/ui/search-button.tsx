import React from "react";
import { But<PERSON> } from "./button";
import { useTranslation } from "react-i18next";

interface SearchButtonProps {
  onClick: () => void;
  className?: string;
  hasErrors?: boolean;
}

const SearchButton: React.FC<SearchButtonProps> = ({
  onClick,
  className = "",
  hasErrors = false,
}) => {
  const { t } = useTranslation();

  return (
    <div className="w-full mb-1">
      <Button
        className={`w-full h-12 bg-[#07F668] hover:bg-[#00C853] text-stone-950 rounded-full font-medium cursor-pointer ${
          hasErrors ? "" : ""
        } ${className}`}
        onClick={onClick}
      >
        {t("searchForm.search", "Search")}
      </Button>
    </div>
  );
};

export default SearchButton;
