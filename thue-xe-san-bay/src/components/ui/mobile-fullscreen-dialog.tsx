import * as React from "react"
import * as DialogPrimitive from "@radix-ui/react-dialog"
import { X } from "lucide-react"
import { cn } from "@/lib/utils"

interface MobileFullscreenDialogProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title?: string;
  className?: string;
}

const MobileFullscreenDialog: React.FC<MobileFullscreenDialogProps> = ({
  isOpen,
  onClose,
  children,
  title,
  className
}) => {
  // Disable body scroll when modal is open
  React.useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
      document.documentElement.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
      document.documentElement.style.overflow = '';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = '';
      document.documentElement.style.overflow = '';
    };
  }, [isOpen]);

  return (
    <DialogPrimitive.Root open={isOpen} onOpenChange={onClose}>
      <DialogPrimitive.Portal>
        <DialogPrimitive.Overlay className="fixed inset-0 z-[9999] bg-black/50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
        <DialogPrimitive.Content
          className={cn(
            "fixed inset-0 z-[9999] bg-black flex flex-col",
            "data-[state=open]:animate-in data-[state=closed]:animate-out",
            "data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
            "data-[state=closed]:slide-out-to-bottom-[100%] data-[state=open]:slide-in-from-bottom-[100%]",
            "duration-300",
            className
          )}
        >
          {/* Header with close button */}
          {(title || onClose) && (
            <div className="flex-shrink-0 bg-black p-4 flex items-start justify-between border-b border-gray-800">
              {title && (
                <h1 className="text-white text-lg font-semibold pt-2">
                  {title}
                </h1>
              )}
              <DialogPrimitive.Close asChild>
                <button
                  onClick={onClose}
                  className="text-white p-2 hover:bg-gray-800 rounded-full transition-colors ml-auto flex-shrink-0"
                >
                  <X className="w-6 h-6" />
                  <span className="sr-only">Close</span>
                </button>
              </DialogPrimitive.Close>
            </div>
          )}
          
          {/* Content */}
          <div className="flex-1 overflow-y-auto bg-black">
            {children}
          </div>
        </DialogPrimitive.Content>
      </DialogPrimitive.Portal>
    </DialogPrimitive.Root>
  );
};

export default MobileFullscreenDialog;
