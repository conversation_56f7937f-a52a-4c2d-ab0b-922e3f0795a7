import { cn } from "@/lib/utils";
import { useLoadingStore } from "@/stores";
import { useEffect } from "react";

interface LoadingProps {
  className?: string;
}

const Loading = ({ className }: LoadingProps) => {
  const { isLoading, message } = useLoadingStore();

  // Ngăn scroll khi loading hiển thị
  useEffect(() => {
    if (isLoading) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isLoading]);

  if (!isLoading) return null;

  return (
    <div className={cn(
      "fixed inset-0 bg-black/50 backdrop-blur-sm z-[9999] flex flex-col items-center justify-center",
      className
    )}>
      <div className="relative w-32 h-32 flex items-center justify-center">
        {/* Logo */}
        <img 
          src={`${import.meta.env.VITE_BASE_PATH}/images/logo/logo-gf.svg`}
          alt="Green Fly Logo" 
          className="w-24 h-24 z-10"
        />
        
        {/* Vòng xoay */}
        <div className="absolute inset-0 border-4 border-transparent border-t-[#07F668] rounded-full animate-spin"></div>
        <div className="absolute inset-1 border-4 border-transparent border-r-white rounded-full animate-spin" style={{ animationDirection: 'reverse' }}></div>
      </div>
      
      {message && (
        <p className="mt-4 text-white font-medium">{message}</p>
      )}
    </div>
  );
};

export { Loading }; 