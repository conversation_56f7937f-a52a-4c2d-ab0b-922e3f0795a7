import { Dialog, DialogContent } from "./dialog";

import React from "react";

interface DialogWrapperProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  maxWidth?: string;
  padding?: string;
  mobileFullHeight?: boolean;
  backgroundClass?: string;
}

const DialogWrapper: React.FC<DialogWrapperProps> = ({
  isOpen,
  onClose,
  children,
  maxWidth = "1200px",
  padding = "p-8",
  mobileFullHeight = false,
  backgroundClass = "bg-background",
}) => {
  const mobileHeightClass = mobileFullHeight 
    ? "sm:max-h-[90vh] max-h-[95vh]" 
    : "max-h-[90vh]";
    
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={`!max-w-[${maxWidth}] !w-[95vw] !${padding} ${mobileHeightClass} ${backgroundClass}`}>
        {children}
      </DialogContent>
    </Dialog>
  );
};

export default DialogWrapper;
