import * as React from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { DayPicker } from "react-day-picker";

import { cn } from "@/lib/utils";
import { buttonVariants } from "@/components/ui/button";

function EnhancedCalendar({
  className,
  classNames,
  showOutsideDays = true,
  ...props
}: React.ComponentProps<typeof DayPicker>) {
  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn("p-3 cursor-default", className)}
      classNames={{
        months: "flex flex-col sm:flex-row space-y-4 sm:space-x-6 sm:space-y-0 w-full",
        month: "space-y-6 w-full",
        caption: "flex justify-center pt-2 pb-4 relative items-center",
        caption_label: "text-base font-semibold text-gray-800 tracking-wide",
        nav: "space-x-1 flex items-center",
        nav_button: cn(
          buttonVariants({ variant: "outline" }),
          "h-9 w-9 bg-white p-0 hover:bg-gray-100 border border-gray-200 rounded-full transition-colors duration-200 cursor-pointer"
        ),
        nav_button_previous: "absolute left-1",
        nav_button_next: "absolute right-1",
        table: "w-full border-collapse space-y-2",
        head_row: "flex mb-2 justify-around",
        head_cell:
          "text-gray-500 rounded-md w-8 sm:w-10 font-medium text-xs sm:text-sm m-0.5 text-center uppercase tracking-wider",
        row: "flex w-full mt-2 justify-around",
        cell: cn(
          "relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-transparent",
          props.mode === "range"
            ? "[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md"
            : "[&:has([aria-selected])]:rounded-md"
        ),
        day: cn(
          buttonVariants({ variant: "ghost" }),
          "h-8 w-8 sm:h-10 sm:w-10 p-0 font-normal text-sm sm:text-base aria-selected:opacity-100 hover:bg-gray-100 transition-colors duration-200 cursor-pointer"
        ),
        day_selected:
          "bg-[#07F668] text-black hover:bg-[#00C853] hover:text-black focus:bg-[#00C853] focus:text-black rounded-full shadow-sm transition-all duration-200 cursor-pointer",
        day_today: "bg-gray-100 text-gray-900 font-medium rounded-full cursor-pointer",
        day_outside: "text-gray-400 opacity-50 cursor-pointer",
        day_disabled: "text-gray-300 opacity-50 cursor-not-allowed",
        day_range_middle:
          "aria-selected:bg-gray-100 aria-selected:text-gray-700 cursor-pointer",
        day_hidden: "invisible",
        ...classNames,
      }}
      components={{
        IconLeft: ({ ...props }) => (
          <ChevronLeft className="h-5 w-5 text-gray-600 transition-transform duration-200 hover:scale-110" {...props} />
        ),
        IconRight: ({ ...props }) => (
          <ChevronRight className="h-5 w-5 text-gray-600 transition-transform duration-200 hover:scale-110" {...props} />
        ),
      }}
      {...props}
    />
  );
}

export { EnhancedCalendar };
