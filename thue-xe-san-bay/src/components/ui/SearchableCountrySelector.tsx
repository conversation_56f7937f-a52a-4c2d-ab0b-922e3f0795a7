import React, { useMemo, useState } from 'react';
import { Search, X } from 'lucide-react';

import { PhoneInput } from 'react-international-phone';

// Sample country data for search functionality
const COUNTRIES = [
  { iso2: 'vn', name: 'Vietnam', dialCode: '+84' },
  { iso2: 'us', name: 'United States', dialCode: '+1' },
  { iso2: 'uk', name: 'United Kingdom', dialCode: '+44' },
  { iso2: 'fr', name: 'France', dialCode: '+33' },
  { iso2: 'de', name: 'Germany', dialCode: '+49' },
  { iso2: 'jp', name: 'Japan', dialCode: '+81' },
  { iso2: 'kr', name: 'South Korea', dialCode: '+82' },
  { iso2: 'cn', name: 'China', dialCode: '+86' },
  { iso2: 'au', name: 'Australia', dialCode: '+61' },
  { iso2: 'ca', name: 'Canada', dialCode: '+1' },
  { iso2: 'sg', name: 'Singapore', dialCode: '+65' },
  { iso2: 'th', name: 'Thailand', dialCode: '+66' },
  { iso2: 'my', name: 'Malaysia', dialCode: '+60' },
  { iso2: 'id', name: 'Indonesia', dialCode: '+62' },
  { iso2: 'ph', name: 'Philippines', dialCode: '+63' },
  { iso2: 'in', name: 'India', dialCode: '+91' },
  { iso2: 'br', name: 'Brazil', dialCode: '+55' },
  { iso2: 'mx', name: 'Mexico', dialCode: '+52' },
  { iso2: 'ar', name: 'Argentina', dialCode: '+54' },
  { iso2: 'es', name: 'Spain', dialCode: '+34' },
  { iso2: 'it', name: 'Italy', dialCode: '+39' },
  { iso2: 'nl', name: 'Netherlands', dialCode: '+31' },
  { iso2: 'se', name: 'Sweden', dialCode: '+46' },
  { iso2: 'no', name: 'Norway', dialCode: '+47' },
  { iso2: 'dk', name: 'Denmark', dialCode: '+45' },
  { iso2: 'fi', name: 'Finland', dialCode: '+358' },
  { iso2: 'ch', name: 'Switzerland', dialCode: '+41' },
  { iso2: 'at', name: 'Austria', dialCode: '+43' },
  { iso2: 'be', name: 'Belgium', dialCode: '+32' },
  { iso2: 'ie', name: 'Ireland', dialCode: '+353' },
];

interface SearchableCountrySelectorProps {
  value: string;
  onChange: (value: string) => void;
  error?: boolean;
  placeholder?: string;
  className?: string;
}

const SearchableCountrySelector: React.FC<SearchableCountrySelectorProps> = ({
  value,
  onChange,
  error = false,
  placeholder = "Nhập số điện thoại",
  className = ""
}) => {
  const [showSearch, setShowSearch] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Filter countries based on search query
  const filteredCountries = useMemo(() => {
    if (!searchQuery.trim()) return COUNTRIES;
    
    const query = searchQuery.toLowerCase();
    return COUNTRIES.filter(country => 
      country.name.toLowerCase().includes(query) ||
      country.dialCode.includes(query) ||
      country.iso2.toLowerCase().includes(query)
    );
  }, [searchQuery]);

  const handleCountrySelect = (country: typeof COUNTRIES[0]) => {
    // Create a phone number with the selected country's dial code
    const newValue = country.dialCode;
    onChange(newValue);
    setShowSearch(false);
    setSearchQuery('');
  };

  if (showSearch) {
    return (
      <div className="relative">
        <div className="bg-white border rounded-lg shadow-lg z-50 absolute top-0 left-0 w-80 max-h-96 overflow-hidden">
          {/* Search Header */}
          <div className="p-3 border-b bg-gray-50">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Tìm quốc gia..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#07F668] text-sm"
                autoFocus
              />
              <button
                onClick={() => {
                  setShowSearch(false);
                  setSearchQuery('');
                }}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>
          
          {/* Country List */}
          <div className="max-h-80 overflow-y-auto">
            {filteredCountries.length > 0 ? (
              filteredCountries.map((country) => (
                <div
                  key={country.iso2}
                  onClick={() => handleCountrySelect(country)}
                  className="flex items-center gap-3 px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                >
                  <span className="text-lg">{country.iso2 === 'vn' ? '🇻🇳' : '🌍'}</span>
                  <div className="flex-1">
                    <div className="text-sm font-medium text-gray-900">{country.name}</div>
                    <div className="text-xs text-gray-500">{country.dialCode}</div>
                  </div>
                </div>
              ))
            ) : (
              <div className="px-4 py-8 text-center text-gray-500 text-sm">
                Không tìm thấy quốc gia nào
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      <PhoneInput
        defaultCountry="vn"
        value={value}
        onChange={onChange}
        inputClassName={`w-full h-12 rounded-md border ${
          error
            ? "border-red-500 ring-1 ring-red-500"
            : "border-gray-300"
        } focus:outline-none focus:ring-2 focus:ring-[#07F668] ${className}`}
        placeholder={placeholder}
        countrySelectorStyleProps={{
          buttonStyle: {
            border: error ? '1px solid #ef4444' : '1px solid #e5e7eb',
            borderRight: 'none',
            height: '3rem',
            display: 'flex',
            alignItems: 'center',
            padding: '0 0.5rem',
            position: 'relative'
          },
          dropdownStyleProps: {
            style: {
              maxHeight: '300px',
              overflowY: 'auto',
              border: '1px solid #e5e7eb',
              borderRadius: '0.5rem',
              background: 'white',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
              zIndex: 1000
            },
            listItemStyle: {
              padding: '8px 12px',
              cursor: 'pointer',
              fontSize: '14px',
              borderBottom: '1px solid #f3f4f6'
            }
          }
        }}
        forceDialCode
        hideDropdown={false}
        disableDialCodeAndPrefix={false}
        showDisabledDialCodeAndPrefix={true}
      />
      
      {/* Search Button Overlay */}
      <button
        onClick={() => setShowSearch(true)}
        className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors"
        title="Tìm kiếm quốc gia"
      >
        <Search className="h-4 w-4" />
      </button>
    </div>
  );
};

export default SearchableCountrySelector; 