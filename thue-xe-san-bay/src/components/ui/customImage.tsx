import { Image } from "@mantine/core";

interface CustomImageProps {
  src: string;
  alt?: string;
  className?: string;
  width?: string;
  height?: string;
  objectFit?: string;
  layout?: string;
}

export const CustomImage = (props: CustomImageProps) => {
  const { src, objectFit, layout, ...rest } = props;
  
  const style = {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    objectFit: objectFit as any,
    width: layout === "fill" ? "100%" : undefined,
    height: layout === "fill" ? "100%" : undefined,
  };

  return (
    <Image 
      src={`${import.meta.env.VITE_BASE_PATH}${src}`} 
      style={style}
      {...rest} 
    />
  );
};
