import React from "react";
import { useTranslation } from "react-i18next";

interface FormLabelProps {
  text: string;
  required?: boolean;
  className?: string;
}

const FormLabel: React.FC<FormLabelProps> = ({
  text,
  required = false,
  className = "",
}) => {
  const { t } = useTranslation();

  return (
    <label className={`block text-sm font-medium text-gray-700 py-1 mb-1 ${className}`}>
      {text} {required && <span className="text-red-500">{t("ui.requiredMark", "*")}</span>}
    </label>
  );
};

export default FormLabel;
