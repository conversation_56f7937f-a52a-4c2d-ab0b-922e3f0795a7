import { CarIcon, PackageIcon, Users2Icon } from "lucide-react";

import { CustomImage } from "@/components/ui/customImage";
import { useTranslation } from "react-i18next";
const cars = [
  {
    id: "vf8",
    model: "VF8",
    type: "D-SUV",
    seats: 5,
    trunk: 350,
    logoIcon: "/images/logo/vf8-logo.png",
    image: "/images/vinfast/vf8-blue.png", // replace with your own path
    badgeColor: "text-green-500 border-green-500",
  },
  {
    id: "vf9",
    model: "VF9",
    type: "E-SUV",
    seats: 7,
    trunk: 212,
    logoIcon: "/images/logo/vf9-logo.png",
    image: "/images/vinfast/vf9-grey.png", // replace with your own path
    badgeColor: "text-white border-white",
  },
];

export default function CarShowcase() {
  const { t } = useTranslation();
  return (
    <div className="bg-black center">
      <section className="container mx-auto bg-black text-white py-20 px-4 md:px-16">
        <h2 className="text-xl lg:text-[3.5rem] font-normal font-[Tektur] mb-16 whitespace-pre-line text-center md:text-left">
          {t("showCase.title")}
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mx-auto">
          {cars.map((car) => (
            <div
              key={car.id}
              className="bg-black overflow-hidden border-solid group transition-all duration-300 relative"
            >
              <div className="p-4 font-bold italic md:absolute top:0">
                <CustomImage
                  src={car.logoIcon}
                  alt={car.model}
                  objectFit="contain"
                  width="100px"
                />
              </div>

              <div
                className="bg-no-repeat bg-center"
                style={{
                  backgroundImage: `url('${
                    import.meta.env.VITE_BASE_PATH
                  }/images/car-bg.png')`,
                  backgroundSize: "contain",
                  backgroundPosition: "right",
                }}
              >
                <CustomImage
                  src={car.image}
                  alt={car.model}
                  layout="fill"
                  objectFit="contain"
                  className="mx-auto"
                />
              </div>

              <div
                className="grid grid-cols-1 p-4 bg-black border-b transition-all duration-300
                   border-b-transparent "
              >
                <div>
                  <h3
                    className={`text-xl lg:text-2xl font-[Tektur] font-bold transition-colors duration-300 text-[#07F668]`}
                  >
                    VINFAST {car.model}
                  </h3>
                </div>

                <div className="mt-2 flex flex-row space-y-1 text-md md:text-lg text-white">
                  <div className="flex flex-wrap gap-2">
                    <div className="flex items-center gap-1 px-3 py-1 bg-[rgba(255,255,255,0.1)] text-sm rounded-full shadow-sm">
                      <CarIcon className="w-4 h-4" />
                      {car.type}
                    </div>

                    <div className="flex items-center gap-1 px-3 py-1 bg-[rgba(255,255,255,0.1)] text-sm rounded-full shadow-sm">
                      <Users2Icon className="w-4 h-4" />
                      {t("showCase.seats", {
                        count:
                          typeof car.seats === "string"
                            ? parseInt(car.seats, 10)
                            : car.seats,
                      })}
                    </div>

                    <div className="flex items-center gap-1 px-3 py-1 bg-[rgba(255,255,255,0.1)] text-sm rounded-full shadow-sm">
                      <PackageIcon className="w-4 h-4" />
                      {t("showCase.trunkCapacity", {
                        count: car.trunk,
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>
    </div>
  );
}
