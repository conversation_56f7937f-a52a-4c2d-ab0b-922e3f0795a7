import { FC } from "react";
import { HeadsetIcon, MailIcon, PhoneCallIcon } from "lucide-react";
import { CustomImage } from "../ui/customImage";
import { useTranslation } from "react-i18next";

const Footer: FC = () => {
  const { t } = useTranslation();

  const navigateTo = (url: string) => {
    window.location.href = url;
  };

  return (
    <div className="bg-transparent sm:mx-6 sm:bg-transparent relative">
      <div
        className="max-w-[1512px] p-[3.75vw] text-white xs:rounded-b-none xs:rounded-t-3xl xs:bg-stone-950 pt-[76px] xs:pb-[3.75vw] lg:pt-[3.75vw] xl:rounded-3xl xl:bg-transparent mx-auto"
        style={{
          backgroundImage:
            typeof window !== "undefined" && window.innerWidth < 768
              ? undefined
              : "url(/images/footer-card.png)",
          backgroundSize: "cover",
          backgroundRepeat: "no-repeat",
        }}
      >
        <img
          src={`${import.meta.env.VITE_BASE_PATH}/images/logo/Cara_logo_White_Full.png`}
          width={182}
          height={48}
          alt="Footer logo"
          className="cursor-pointer hover:text-[#07F668] mb-12"
          onClick={() => navigateTo("/")}
        />

        <div className="flex flex-col justify-between gap-10 lg:flex-row">
          <div className="flex w-full flex-col gap-3 lg:w-1/3">
            <p className="font-semibold text-white text-md">
              {t("footer.companyName")}
            </p>
            <p className="text-[#CECECD] text-xs font-normal leading-6">
              {t("footer.businessInfo")}
            </p>
            <p className="text-[#CECECD] text-xs font-normal leading-6">
              {t("footer.address")}
            </p>
            {/* <img src={certificateImage} width={100} height={38} alt='Certificate Image' /> */}
          </div>
          <div className="flex flex-col gap-8">
            <div className="grid grid-cols-2 gap-6 md:grid-cols-3 md:gap-2 lg:gap-2">
              <div className="flex flex-col gap-3">
                <p className="text-[18px] md:text-[20px] font-[500] mb-2 md:mb-4">
                  {t("footer.carRental")}
                </p>
                <p className="text-input hover:underline text-sm md:text-base">
                  {t("footer.shortTerm")}
                </p>
                <p className="text-input hover:underline text-sm md:text-base">
                  {t("footer.longTerm")}
                </p>
                <p className="text-input hover:underline text-sm md:text-base">
                  {t("footer.business")}
                </p>
              </div>
              <div className="flex flex-col gap-3">
                <a
                  href="/"
                  className="text-[18px] md:text-[20px] font-[500] mb-2 md:mb-4"
                >
                  {t("footer.introduction")}
                </a>
                <a
                  href="/ve-chung-toi"
                  className="text-input hover:underline text-sm md:text-base"
                >
                  {t("footer.aboutUs")}
                </a>
                <a
                  href="/policy"
                  className="text-input hover:underline text-sm md:text-base"
                >
                  {t("footer.services")}
                </a>
                <a
                  href="/tin-tuc"
                  className="text-input hover:underline text-sm md:text-base"
                >
                  {t("footer.news")}
                </a>
              </div>
              <div className="flex flex-col gap-6 col-span-2 md:col-span-1">
                <p className="text-[18px] md:text-[20px] font-[500] mb-2 md:mb-4">
                  {t("footer.contact")}
                </p>
                <a
                  className="inline-flex items-center gap-2 hover:underline"
                  href="tel:19001877"
                >
                  <HeadsetIcon className="text-primary shrink-0" size={16} />
                  <p className="font-semibold text-primary text-xs md:text-md">
                    1900 1877
                  </p>
                </a>
                <a
                  className="inline-flex items-center gap-2 text-primary hover:underline"
                  href="tel:0896229555"
                >
                  <PhoneCallIcon
                    size={16}
                    className="text-[#CECECD] shrink-0"
                  />
                  <p className="font-semibold text-[#CECECD] text-xs md:text-md">
                    0896 229 555
                  </p>
                </a>
                <a
                  className="inline-flex items-center gap-2 text-primary hover:underline "
                  href="mailto:<EMAIL>"
                >
                  <MailIcon size={16} className="text-[#CECECD] shrink-0" />
                  <p className="font-semibold text-[#CECECD] text-xs md:text-md">
                    <EMAIL>
                  </p>
                </a>
              </div>
            </div>
          </div>

          <div className="flex flex-row gap-6 xs:justify-center lg:flex-col lg:justify-start">
            <a
              href="https://www.facebook.com/GreenFutureOfficial.Global"
              target="_blank"
            >
              <CustomImage
                src="/images/social/icon-facebook.svg"
                alt="FB logo"
                className="h-8 w-8 lg:h-5 lg:w-5"
              />
            </a>

            <a
              href="https://www.tiktok.com/@greenfuture.official"
              target="_blank"
            >
              <CustomImage
                src="/images/social/icon-tiktok.svg"
                alt="TikTok logo"
                className="h-8 w-8 lg:h-5 lg:w-5"
              />
            </a>

            <a
              href="https://www.instagram.com/greenfuture.global"
              target="_blank"
            >
              <CustomImage
                src="/images/social/icon-insta.svg"
                alt="Insta logo"
                className="h-8 w-8 lg:h-5 lg:w-5"
              />
            </a>

            <a
              href="https://www.youtube.com/@greenfuture.official"
              target="_blank"
            >
              <CustomImage
                src="/images/social/icon-youtube.svg"
                alt="Youtube logo"
                className="h-8 w-8 lg:h-5 lg:w-5"
              />
            </a>
          </div>
        </div>

        <div className="mt-10 flex items-center justify-between lg:gap-10">
          <div className="lg:hidden" />
          <div className="w-full flex-1 grid grid-cols-1 xs:gap-3 lg:flex lg:flex-row lg:items-center lg:justify-between">
            <p className="text-[#CECECD] text-md font-normal w-full text-center lg:text-left">
              {t("footer.copyright")}
            </p>
            <a
              href="/policy/privacies-policy"
              target="_blank"
              className="text-[#CECECD] hover:underline w-full text-center lg:text-right"
            >
              {t("footer.termsConditions")}
            </a>
          </div>
          <div className="flex justify-end lg:w-max">
            <button
              className="flex items-center justify-center rounded-full bg-transparent text-white hover:opacity-80 cursor-pointer"
              onClick={() => window.scrollTo(0, 0)}
              aria-label="Back to Top"
            >
              <CustomImage src="/images/logo/arrow-up.svg" alt="Arrow up" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Footer;
