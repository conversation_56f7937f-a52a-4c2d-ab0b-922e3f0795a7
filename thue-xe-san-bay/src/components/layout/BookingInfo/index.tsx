import {
  Carousel,
  Carousel<PERSON><PERSON>,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { ChevronLeft, ChevronRight } from "lucide-react";
import React from "react";
import BookingCard from "./InfoCard";
import { useTranslation } from "react-i18next";

const infos = [
  {
    key: "info_key",
    icon: "/images/logo/luggage.svg",
  },
  {
    key: "selection_key",
    icon: "/images/logo/car_tag.svg",
  },
  {
    key: "accept_key",
    icon: "/images/logo/check_circle.svg",
  },
  {
    key: "enjoy_key",
    icon: "/images/logo/digital_wellbeing.svg",
  },
];

export default function BookingInfo() {
  const { t } = useTranslation();
  const [api, setApi] = React.useState<CarouselApi | null>(null);
  const [current, setCurrent] = React.useState(0);

  React.useEffect(() => {
    if (!api) return;

    setCurrent(api.selectedScrollSnap());

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap());
    });
  }, [api]);
  return (
    <div className="bg-black">
      <section className="container mx-auto px-4 bg-black text-white py-20">
        <h2 className="text-xl lg:text-[3.5rem] font-normal font-[Tektur] mb-16 whitespace-pre-line text-center md:text-left">
          {t("infos.title")}
        </h2>
        <div
          className="container mx-auto flex justify-center md:justify-start bg-black relative bg-center"
          style={{
            backgroundImage: `url('${
              import.meta.env.VITE_BASE_PATH
            }/images/banner/airport-bg.png')`,
            backgroundSize: "cover",
          }}
        >
          <div className="flex items-center justify-center py-32 md:bg-gradient-to-l md:from-transparent md:to-black">
            <div className="bg-black text-white max-w-md w-full p-12 pt-16 pb-20 rounded-lg space-y-6">
              <Carousel
                setApi={setApi}
                opts={{
                  align: "center",
                  loop: true,
                }}
                className="w-full max-w-md"
              >
                <CarouselContent>
                  {infos.map((item) => (
                    <CarouselItem key={item.key}>
                      <BookingCard
                        title={t(`infos.${item.key}.title`)}
                        content={t(`infos.${item.key}.content`)}
                        icon={item.icon}
                      />
                    </CarouselItem>
                  ))}
                </CarouselContent>

                <div className="flex  center justify-center pt-4">
                  <CarouselPrevious className="absolute left-0 md:-left-6 translate-y-40 z-10 bg-black">
                    <ChevronLeft className="w-5 h-5" />
                  </CarouselPrevious>
                  <div className="flex space-x-2 center justify-center align-center">
                    {Array.from([0, 1, 2, 3]).map((item) => {
                      return (
                        <span
                          key={`dot-${item}`}
                          className={`rounded-full transition-all ${
                            item === current
                              ? "w-5 h-2 bg-green-500"
                              : "w-2 h-2 rounded-full bg-gray-500"
                          }`}
                        />
                      );
                    })}
                  </div>
                  <CarouselNext className="absolute right-0 md:-right-6 translate-y-40 z-10 bg-black">
                    <ChevronRight className="w-5 h-5" />
                  </CarouselNext>
                </div>
              </Carousel>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
