import { CustomImage } from "@/components/ui/customImage";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const BookingCard = ({ icon, title, content }: any) => {
  return (
    <>
      <div className="ml-6">
        {icon && <CustomImage src={icon} alt={title} className="h-18 w-18 lg:h-24 lg:w-24" />}
      </div>
      <div className="px-8">
        <h2 className="text-lg lg:text-[36px] font-semibold leading-tight font-[EncodeSansExpanded]">
          {title}
        </h2>
        <p className="mt-4 text-base lg:text-lg text-[#ABABAB]">{content}</p>
      </div>
    </>
  );
};

export default BookingCard;
