import React from "react";
import { useTranslation } from "react-i18next";

interface HeroSectionProps {
  children: React.ReactNode;
}

const HeroSection = ({ children }: HeroSectionProps) => {
  const { t } = useTranslation();

  return (
    <section className="h-[calc(100vh-10rem)] w-full bg-background-menu relative">
      {/* Mobile background */}
      <div
        className="absolute inset-0 bg-cover bg-center md:hidden"
        style={{
          backgroundImage: `url('${
            import.meta.env.VITE_BASE_PATH
          }/images/banner/banner-hero-mobile.png')`,
        }}
      ></div>
      
      {/* Desktop background */}
      <div
        className="absolute inset-0 bg-cover bg-center hidden md:block"
        style={{
          backgroundImage: `url('${
            import.meta.env.VITE_BASE_PATH
          }/images/banner/banner-hero.webp')`,
        }}
      ></div>
      
      <div className="container mx-auto px-2 py-3 flex flex-col h-full relative z-10 md:pt-20">
        <div className="w-full">
          <h1 className="p-2 md:p-0 text-left text-2xl lg:text-[3.75rem] font-[Tektur] text-primary whitespace-pre-line">
            {t("hero.title", "Đẳng cấp vươn tầm\nThoải mái chọn hành trình")}
          </h1>
        </div>

        {/* Form thuê xe */}
        {children}
      </div>
    </section>
  );
};

export default HeroSection;
