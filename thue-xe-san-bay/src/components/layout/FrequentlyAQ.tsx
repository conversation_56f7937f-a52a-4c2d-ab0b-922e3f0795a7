import {
  Accordion,
  Accordion<PERSON>ontent,
  Accordion<PERSON>tem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useTranslation } from "react-i18next";

// Define type for FAQ item
interface FaqItem {
  question: string;
  answer: string;
}

// Define type for FAQ item
interface FaqItem {
  question: string;
  answer: string;
}

const FrequentlyAQ = () => {
  const { t } = useTranslation();

  // Use type assertion to tell TypeScript that we expect an array of FaqItem
  const faqItems = t("faq.items", { returnObjects: true }) as FaqItem[];

  // Use type assertion to tell TypeScript that we expect an array of FaqItem

  return (
    <div className="bg-black overflow-hidden py-8 lg:py-16 lg:pt-32 p-2">
      <div className="container mx-auto px-14">
        <h2 className="text-xl lg:text-[3.5rem] font-normal font-[Tektur] mb-16 whitespace-pre-line text-center md:text-left bg-gradient-to-b from-white via-white to-[#C2C2C2] bg-clip-text text-transparent">
          {t("faq.title")}
        </h2>

        {/* List FAQ */}
        <Accordion type="single" collapsible className="w-full md:ml-12">
          {faqItems.map((faq: FaqItem, index: number) => (
            <AccordionItem
              key={index}
              value={`item-${index}`}
              className="border-b border-neutral-800"
            >
              <AccordionTrigger className="text-white text-xl text-[18px] lg:text-lg py-4 lg:py-6 hover:no-underline data-[state=open]:text-[#07F668] font-semibold cursor-pointer text-left">
                {faq.question}
              </AccordionTrigger>
              <AccordionContent className="text-gray-300 font-[400] text-md lg:text-base leading-7 lg:leading-9">
                {faq.answer}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </div>
  );
};

export default FrequentlyAQ;
