import { useTranslation } from "react-i18next";

export default function CommitmentSection() {
  const { t } = useTranslation();
  const features = t("commitment.features", { returnObjects: true }) as {
    number: string;
    title: string;
    description: string;
  }[];
  return (
    <div className="bg-black center pt-26 md:pt-0">
      <section className="container mx-auto bg-black text-white py-20 px-4 md:pt-24">
        <h2 className="text-xl lg:text-[3.5rem] font-normal font-[Tektur] mb-16 text-center md:text-left">
          {t("commitment.title")}
          <br />
          <span className="text-[#C2C2C2]">{t("commitment.title2line")}</span>
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mx-auto text-center md:text-left">
          {features.map((feature, index) => (
            <div key={index} className="relative">
              <div className="absolute -top-16 left-0 right-0 flex justify-center text-[8rem] lg:text-[9.25rem] font-bold text-gray-300 font-[EncodeSansExpanded] opacity-20 [mask-image:linear-gradient(to_bottom,white_10%,transparent)]">
                {feature.number}
              </div>
              <h3 className="text-lg lg:text-xl font-[EncodeSansExpanded] font-semibold whitespace-pre-line mb-2 pt-20 bg-gradient-to-b">
                {feature.title}
              </h3>
              <p className="text-[#ABABAB] whitespace-pre-line font-medium text-base lg:text-lg">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </section>
    </div>
  );
}
