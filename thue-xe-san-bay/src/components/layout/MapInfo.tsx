import { CustomImage } from "../ui/customImage";
import { ArrowIcon } from "../../assets/icons";
import { useTranslation } from "react-i18next";

const MapInfo = () => {
  const { t } = useTranslation();

  const scrollToSearchForm = () => {
    const searchForm = document.getElementById("search-form");
    if (searchForm) {
      // Tính toán vị trí cuộn với offset
      const offsetPosition =
        searchForm.getBoundingClientRect().top + window.scrollY - 100; // Thêm offset -100px để hiển thị cao hơn

      // Cuộn đến vị trí đã điều chỉnh
      window.scrollTo({
        top: offsetPosition,
        behavior: "smooth",
      });
    }
  };

  const airports = [
    {
      city: t("mapInfo.airports.danang.city", "Đà Nẵng"),
      prefix: t("mapInfo.airports.prefix", "Sân bay quốc tế"),
      location: t("mapInfo.airports.danang.location", "Đà Nẵng"),
      status: "active",
    },
    {
      city: t("mapInfo.airports.hanoi.city", "Hà Nội"),
      prefix: t("mapInfo.airports.prefix", "Sân bay quốc tế"),
      location: t("mapInfo.airports.hanoi.location", "Nội Bài"),
      status: "active",
    },
    {
      city: t("mapInfo.airports.hochiminh.city", "Hồ Chí Minh"),
      prefix: t("mapInfo.airports.prefix", "Sân bay quốc tế"),
      location: t("mapInfo.airports.hochiminh.location", "Tân Sơn Nhất"),
      status: "active",
    },
  ];

  return (
    <div className="bg-black overflow-hidden py-8">
      <div className="container mx-auto px-4 md:px-16">
        <h2 className="text-xl lg:text-[3.5rem] font-normal font-[Tektur] mb-16 whitespace-pre-line text-center md:text-left bg-gradient-to-b from-white via-white to-[#C2C2C2] bg-clip-text text-transparent">
          {t("mapInfo.title", "Sẵn sàng phục vụ tại")}
        </h2>

        {/* Chia 2 column 50/50 */}
        <div className="flex flex-col md:flex-row">
          <div className="w-full md:w-1/2 flex flex-col justify-center items-center">
            <CustomImage
              src="/images/logo/map.svg"
              alt={t("mapInfo.mapAlt", "map-info")}
            />
          </div>
          <div className="w-full md:w-1/2">
            <div className="mt-10 md:mt-0 flex flex-col overflow-auto">
              {airports.map((airport, index) => (
                <div key={index}>
                  <div
                    className={`p-4 md:p-8 ${
                      airport.status === "active"
                        ? airport.city === t("mapInfo.airports.danang.city", "Đà Nẵng")
                          ? "bg-[#00FF66]"
                          : "bg-[#1A1A1A]"
                        : "bg-[#1A1A1A]"
                    }`}
                  >
                    <div
                      className={`font-[EncodeSansExpanded] ${
                        airport.status === "active"
                          ? airport.city === t("mapInfo.airports.danang.city", "Đà Nẵng")
                            ? "text-[#4D4D4D]"
                            : "text-[#686868]"
                          : "text-[#686868]"
                      } text-base md:text-[28px] font-[400] mb-4`}
                    >
                      {airport.city}
                    </div>
                    <div
                      className={`font-[EncodeSansExpanded] text-lg md:text-2xl mb-1 break-words ${
                        airport.status === "active"
                          ? airport.city === t("mapInfo.airports.danang.city", "Đà Nẵng")
                            ? "text-black"
                            : "text-white"
                          : "text-white"
                      }`}
                    >
                      {airport.prefix}
                    </div>
                    <div
                      className={`font-[EncodeSansExpanded] text-lg md:text-2xl mb-6 md:mb-10 break-words ${
                        airport.status === "active"
                          ? airport.city === t("mapInfo.airports.danang.city", "Đà Nẵng")
                            ? "text-black"
                            : "text-white"
                          : "text-white"
                      }`}
                    >
                      {airport.location}
                    </div>

                    <div className="flex justify-end">
                      {airport.status === "active" ? (
                        <button
                          className="flex items-center cursor-pointer"
                          onClick={scrollToSearchForm}
                        >
                          <span className={`mr-8 text-base md:text-xl font-[400] ${
                            airport.city === t("mapInfo.airports.danang.city", "Đà Nẵng")
                              ? "text-black"
                              : "text-white"
                          }`}>
                            {t("mapInfo.bookNow", "Đặt xe")}
                          </span>
                          <span className={`inline-flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                            airport.city === t("mapInfo.airports.danang.city", "Đà Nẵng")
                              ? "border-black"
                              : "border-white"
                          }`}>
                            <ArrowIcon
                              color={airport.city !== t("mapInfo.airports.danang.city", "Đà Nẵng") ? "white" : "black"}
                            />
                          </span>
                        </button>
                      ) : (
                        <div className="text-[#07F668] text-base md:text-[28px] font-semibold">
                          {t("mapInfo.comingSoon", "Coming soon")}
                        </div>
                      )}
                    </div>
                  </div>
                  {index < airports.length - 1 && (
                    <div className="h-[1px] w-full bg-gradient-to-r from-transparent to-[#B3B3B3]"></div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MapInfo;
