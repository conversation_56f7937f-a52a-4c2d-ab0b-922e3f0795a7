import { MenuIcon, USFlag, VietnameseFlag } from "../../assets/icons";
import { useEffect, useState } from "react";

import { CustomImage } from "../ui/customImage";
import { buildUrlWithParams } from "../../lib/utils";
import { useTranslation } from "react-i18next";

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isLangDropdownOpen, setIsLangDropdownOpen] = useState(false);
  const { t, i18n } = useTranslation();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const toggleLangDropdown = () => {
    setIsLangDropdownOpen(!isLangDropdownOpen);
  };

  const changeLanguage = (lang: string) => {
    i18n.changeLanguage(lang);
    setIsLangDropdownOpen(false);

    // Chuyển hướng URL khi đổi ngôn ngữ, giữ nguyên tham số URL
    const basePath = "/thue-xe-san-bay";

    // Xử lý đổi URL dựa vào ngôn ngữ
    if (lang === "en") {
      // Nếu chuyển sang tiếng Anh
      if (!window.location.pathname.includes("/en")) {
        // Nếu URL hiện tại không có /en, thêm vào và giữ nguyên tham số
        const newUrl = buildUrlWithParams(basePath, true);
        window.history.pushState({}, "", newUrl);
      }
    } else {
      // Nếu chuyển sang tiếng Việt
      if (window.location.pathname.includes("/en")) {
        // Nếu URL hiện tại có /en, xóa đi và giữ nguyên tham số
        const newUrl = buildUrlWithParams(basePath, false);
        window.history.pushState({}, "", newUrl);
      }
    }
  };

  // Theo dõi URL để cập nhật ngôn ngữ
  useEffect(() => {
    const handleUrlChange = () => {
      const path = window.location.pathname;
      const isEnglishPath = path.includes("/en") || path.endsWith("/en");
      const currentLang = i18n.language;

      if (isEnglishPath && currentLang !== "en") {
        i18n.changeLanguage("en");
      } else if (!isEnglishPath && currentLang !== "vi") {
        i18n.changeLanguage("vi");
      }
    };

    // Lắng nghe sự kiện popstate (khi người dùng nhấn nút back/forward)
    window.addEventListener("popstate", handleUrlChange);

    // Kiểm tra URL khi component mount
    handleUrlChange();

    return () => {
      window.removeEventListener("popstate", handleUrlChange);
    };
  }, [i18n]);

  // Show appropriate flag based on current language
  const LangIcon = i18n.language === "vi" ? VietnameseFlag : USFlag;

  return (
    <header className="fixed top-0 left-0 w-full bg-background-menu text-white z-50">
      <div className="container mx-auto py-3 flex justify-between items-center">
        {/* Logo */}
        <div className="mx-4 flex items-center">
          <a href="/" className="flex items-center">
            <CustomImage
              src="/images/logo/logo-gf.svg"
              alt="logo"
              className="h-20 w-20"
            />
          </a>
        </div>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex space-x-8">
          <a
            href="/thue-xe-tu-lai"
            className="font-medium hover:text-primary transition"
          >
            {t("nav.bookCar", "Đặt xe")}
          </a>
          <a
            href="/xe-luot"
            className="font-medium hover:text-primary transition"
          >
            {t("nav.buyUsedCar", "Mua xe cũ chính hãng")}
          </a>
          <a
            href="/thue-xe-san-bay"
            className="font-medium text-primary transition"
          >
            {t("nav.airportCars", "Thuê Xe Sân Bay")}
          </a>
          <a
            href="/ve-chung-toi"
            className="font-medium hover:text-primary transition"
          >
            {t("nav.about", "Giới thiệu")}
          </a>
          <a
            href="/tin-tuc"
            className="font-medium hover:text-primary transition"
          >
            {t("nav.news", "Tin tức")}
          </a>
          <div className="relative">
            <button
              onClick={toggleLangDropdown}
              className="flex items-center font-medium hover:text-primary transition cursor-pointer"
              title={t("language")}
            >
              <LangIcon />
            </button>

            {isLangDropdownOpen && (
              <div className="absolute right-0 mt-2 w-40 bg-background-menu rounded-md shadow-lg z-50">
                {i18n.language === "vi" ? (
                  <button
                    className="w-full flex items-center px-4 py-2 text-sm text-white hover:bg-gray-700 hover:text-primary cursor-pointer"
                    onClick={() => changeLanguage("en")}
                  >
                    <USFlag />
                    <span className="ml-2">English</span>
                  </button>
                ) : (
                  <button
                    className="w-full flex items-center px-4 py-2 text-sm text-white hover:bg-gray-700 hover:text-primary cursor-pointer"
                    onClick={() => changeLanguage("vi")}
                  >
                    <VietnameseFlag />
                    <span className="ml-2">Tiếng Việt</span>
                  </button>
                )}
              </div>
            )}
          </div>
        </nav>

        {/* Mobile Menu Button */}
        <button className="md:hidden focus:outline-none mr-4" onClick={toggleMenu}>
          <MenuIcon />
        </button>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden bg-black">
          <div className="container mx-auto px-4 py-3 flex flex-col space-y-4">
            <a
              href="/thue-xe-tu-lai"
              className="font-medium hover:text-primary transition"
            >
              {t("nav.bookCar", "Đặt xe")}
            </a>
            <a
              href="/xe-luot"
              className="font-medium hover:text-primary transition"
            >
              {t("nav.buyUsedCar", "Mua xe cũ chính hãng")}
            </a>
            <a
              href="/thue-xe-san-bay"
              className="font-medium text-primary transition"
            >
              {t("nav.airportCars", "Thuê Xe Sân Bay")}
            </a>
            <a
              href="/ve-chung-toi"
              className="font-medium hover:text-primary transition"
            >
              {t("nav.about", "Giới thiệu")}
            </a>
            <a
              href="/tin-tuc"
              className="font-medium hover:text-primary transition"
            >
              {t("nav.news", "Tin tức")}
            </a>
            <div className="relative">
              <button
                onClick={toggleLangDropdown}
                className="flex items-center font-medium hover:text-primary transition cursor-pointer"
                title={t("language")}
              >
                <LangIcon />
                <span className="ml-2">
                  {i18n.language === "vi" ? "Tiếng Việt" : "English"}
                </span>
              </button>

              {isLangDropdownOpen && (
                <div className="mt-2 ml-6 bg-gray-900 rounded-md p-2">
                  {i18n.language === "vi" ? (
                    <button
                      className="w-full flex items-center py-2 text-sm text-white hover:text-primary cursor-pointer"
                      onClick={() => changeLanguage("en")}
                    >
                      <USFlag />
                      <span className="ml-2">English</span>
                    </button>
                  ) : (
                    <button
                      className="w-full flex items-center py-2 text-sm text-white hover:text-primary cursor-pointer"
                      onClick={() => changeLanguage("vi")}
                    >
                      <VietnameseFlag />
                      <span className="ml-2">Tiếng Việt</span>
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
