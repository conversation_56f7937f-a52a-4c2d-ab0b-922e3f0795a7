import React from "react";
import { X, MapPin, Clock, CheckIcon } from "lucide-react";
import { Button } from "../ui/button";
import { CustomImage } from "../ui/customImage";
import { useTranslation } from "react-i18next";
import "./MobileTestModal.css";

interface MobileTestModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const MobileTestModal: React.FC<MobileTestModalProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation();

  // Mock data cho test
  const mockCarData = [
    {
      id: 1,
      model: "VF8",
      price: 850000,
      formattedPrice: "850.000 VNĐ",
      image: "/images/vinfast/vf8.png",
      vehicleType: "VF8" as const,
    },
    {
      id: 2,
      model: "VF9",
      price: 1200000,
      formattedPrice: "1.200.000 VNĐ",
      image: "/images/vinfast/vf9.png",
      vehicleType: "VF9" as const,
    },
  ];

  const mockTripInfo = {
    pickup: "Sân bay Nội Bài (HAN)",
    destination: "Trung tâm Hà Nội",
    time: "14:30",
    date: "25/12/2024",
    distance: "35",
    travelTime: "45m",
  };

  if (!isOpen) return null;

  // Disable body scroll when modal is open
  React.useEffect(() => {
    if (isOpen) {
      document.body.classList.add('modal-open');
      document.documentElement.classList.add('modal-open');
    } else {
      document.body.classList.remove('modal-open');
      document.documentElement.classList.remove('modal-open');
    }

    // Cleanup on unmount
    return () => {
      document.body.classList.remove('modal-open');
      document.documentElement.classList.remove('modal-open');
    };
  }, [isOpen]);

  return (
    <div className="mobile-test-modal">
      {/* Header với nút đóng - fixed */}
      <div className="bg-black p-4 flex items-center justify-between flex-shrink-0 border-b border-gray-800">
        <h1 className="text-white text-lg font-semibold">
          {t("tripInfo.serviceTitle", "Lựa chọn dịch vụ đưa đón sân bay GF VIP")}
        </h1>
        <button
          onClick={onClose}
          className="text-white p-2 hover:bg-gray-800 rounded-full transition-colors"
        >
          <X className="w-6 h-6" />
        </button>
      </div>

      {/* Nội dung cuộn được - flex-1 để chiếm hết không gian còn lại */}
      <div className="mobile-test-modal-content p-4 space-y-6">
        {/* Thông tin chuyến đi */}
        <div className="bg-white rounded-lg p-4">
          <div className="space-y-4">
            {/* Thông tin địa điểm */}
            <div className="relative">
              {/* Đường thẳng dọc */}
              <div className="absolute left-[11px] top-6 bottom-2 w-0.5 bg-gray-600 z-0"></div>

              {/* Điểm đón */}
              <div className="flex items-start mb-4 relative z-10">
                <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center bg-gray-700 rounded-full mr-3">
                  <MapPin className="h-4 w-4 text-white" />
                </div>
                <div className="flex-1">
                  <p className="font-medium text-sm text-[#090806]">
                    {mockTripInfo.pickup}
                  </p>
                </div>
              </div>

              {/* Điểm đến */}
              <div className="flex items-start relative z-10">
                <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center bg-gray-700 rounded-full mr-3">
                  <MapPin className="h-4 w-4 text-white" />
                </div>
                <div className="flex-1">
                  <p className="font-medium text-sm text-[#090806]">
                    {mockTripInfo.destination}
                  </p>
                </div>
              </div>
            </div>

            {/* Đường phân cách */}
            <div className="h-px bg-[#CECECD]"></div>

            {/* Chi tiết chuyến đi */}
            <div className="space-y-2">
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-2 text-gray-400" />
                <span className="font-medium text-sm text-[#090806]">
                  Giờ đón: {mockTripInfo.time} - {mockTripInfo.date}
                </span>
              </div>
              <div className="flex items-center">
                <MapPin className="w-4 h-4 mr-2 text-gray-400" />
                <span className="font-medium text-sm text-[#090806]">
                  Quãng đường: ~{mockTripInfo.distance} km
                </span>
              </div>
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-2 text-gray-400" />
                <span className="font-medium text-sm text-[#090806]">
                  Thời gian di chuyển: ~{mockTripInfo.travelTime}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Lựa chọn xe */}
        <div className="space-y-4 pb-8">
          {mockCarData.map((car) => (
            <div
              key={car.id}
              className="bg-white rounded-2xl p-4 border border-gray-800"
            >
              <div className="space-y-4">
                {/* Hình ảnh xe */}
                <div className="flex justify-center">
                  <CustomImage
                    src={car.image}
                    className="w-full h-auto object-contain max-h-[150px]"
                  />
                </div>

                {/* Tiêu đề và giá */}
                <div className="flex justify-between items-center">
                  <div className="bg-black px-3 py-1 text-white text-[20px] font-[Tektur] font-bold rounded-xs">
                    {car.model === "VF8" ? "BUSINESS" : "FIRST CLASS"}
                  </div>
                  <div className="text-right">
                    <span className="text-[20px] font-medium text-black">
                      {car.formattedPrice}
                    </span>
                  </div>
                </div>

                {/* Chi tiết xe */}
                <div className="grid grid-cols-3 gap-2 rounded-lg">
                  <div className="flex flex-col items-start bg-[#E6E6E6] rounded p-1.5 pb-4">
                    <div className="w-4 h-4 text-gray-600 mb-1">👥</div>
                    <p className="text-[14px] text-[#090806] text-left">
                      {car.vehicleType === "VF8" ? "5 chỗ" : "6-7 chỗ"}
                    </p>
                  </div>
                  <div className="flex flex-col items-start bg-[#E6E6E6] rounded p-1">
                    <div className="w-4 h-4 text-gray-600 mb-1">🚗</div>
                    <p className="text-[14px] text-[#090806] text-left">
                      {car.vehicleType === "VF8" ? "D-SUV" : "E-SUV"}
                    </p>
                  </div>
                  <div className="flex flex-col items-start bg-[#E6E6E6] rounded p-1">
                    <div className="w-4 h-4 text-gray-600 mb-1">📦</div>
                    <p className="text-[14px] text-[#090806] text-left">
                      {car.vehicleType === "VF8" ? "Cốp rộng rãi" : "Cốp siêu rộng rãi"}
                    </p>
                  </div>
                </div>

                {/* Danh sách tính năng */}
                <div className="grid grid-cols-1 gap-2">
                  <div className="space-y-2">
                    <div className="flex items-start">
                      <CheckIcon className="h-4 w-4 text-[#07F668] mr-2 mt-0.5 flex-shrink-0" />
                      <p className="text-xs text-black">
                        {car.vehicleType === "VF8" ? "5 chỗ ngồi" : "6-7 chỗ ngồi"}
                      </p>
                    </div>
                    <div className="flex items-start">
                      <CheckIcon className="h-4 w-4 text-[#07F668] mr-2 mt-0.5 flex-shrink-0" />
                      <p className="text-xs text-black">Hệ thống bảo vệ 11 túi khí</p>
                    </div>
                    <div className="flex items-start">
                      <CheckIcon className="h-4 w-4 text-[#07F668] mr-2 mt-0.5 flex-shrink-0" />
                      <p className="text-xs text-black">Sạc điện thoại miễn phí</p>
                    </div>
                    <div className="flex items-start">
                      <CheckIcon className="h-4 w-4 text-[#07F668] mr-2 mt-0.5 flex-shrink-0" />
                      <p className="text-xs text-black">Tài xế tận tâm, chuyên nghiệp</p>
                    </div>
                    <div className="flex items-start">
                      <CheckIcon className="h-4 w-4 text-[#07F668] mr-2 mt-0.5 flex-shrink-0" />
                      <p className="text-xs text-black">
                        {car.vehicleType === "VF8" ? "Cốp xe rộng rãi" : "Cốp xe siêu rộng rãi"}
                      </p>
                    </div>
                    {car.vehicleType === "VF9" && (
                      <div className="flex items-start">
                        <CheckIcon className="h-4 w-4 text-[#07F668] mr-2 mt-0.5 flex-shrink-0" />
                        <p className="text-xs text-black">Hệ thống ghế sưởi, massage</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Nút chọn */}
                <Button
                  className="w-full h-12 bg-white border border-black text-[#090806] rounded-full text-[16px] font-semibold hover:bg-gray-200 transition-colors"
                  onClick={() => {
                    alert(`Đã chọn ${car.model}!`);
                    onClose();
                  }}
                >
                  Chọn dịch vụ {car.model}
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default MobileTestModal;
