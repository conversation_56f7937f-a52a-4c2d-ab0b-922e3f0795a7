@font-face {
  font-family: "SVN-Poppins";
  src: url("./assets/fonts/SVN-Poppins-400.otf") format("opentype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "SVN-Poppins";
  src: url("./assets/fonts/SVN-Poppins-100.otf") format("opentype");
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "SVN-Poppins";
  src: url("./assets/fonts/SVN-Poppins-200.otf") format("opentype");
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "SVN-Poppins";
  src: url("./assets/fonts/SVN-Poppins-300.otf") format("opentype");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "SVN-Poppins";
  src: url("./assets/fonts/SVN-Poppins-500.otf") format("opentype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "SVN-Poppins";
  src: url("./assets/fonts/SVN-Poppins-600.otf") format("opentype");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "SVN-Poppins";
  src: url("./assets/fonts/SVN-Poppins-700.otf") format("opentype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "SVN-Poppins";
  src: url("./assets/fonts/SVN-Poppins-800.otf") format("opentype");
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "SVN-Poppins";
  src: url("./assets/fonts/SVN-Poppins-900.otf") format("opentype");
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Poppins";
  src: url("./assets/fonts/Poppins.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Tektur";
  src: url("./assets/fonts/Tektur.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Redressed";
  src: url("./assets/fonts/SVN-Redressed.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "OldStandardTT";
  src: url("./assets/fonts/OldStandardTT-Regular.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "OldStandardTTBold";
  src: url("./assets/fonts/OldStandardTT-Bold.ttf") format("truetype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "OldStandardTTItalic";
  src: url("./assets/fonts/OldStandardTT-Italic.ttf") format("truetype");
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "EncodeSansExpanded";
  src: url("./assets/fonts/EncodeSansExpanded-Regular.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

html,
body {
  font-family: "SVN-Poppins", sans-serif;
  font-size: 16px;
  line-height: 1.5;
}

/* Prevent zooming on iOS when focusing on inputs */
input, 
select, 
textarea {
  font-size: 16px !important; /* iOS won't zoom in if font size is at least 16px */
  touch-action: manipulation; /* Prevent double-tap to zoom */
}

/* Add a meta tag to disable zooming via JavaScript */
@media screen and (max-width: 767px) {
  input:focus,
  select:focus,
  textarea:focus {
    font-size: 16px !important;
  }
}
