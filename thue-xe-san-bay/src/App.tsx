import "./App.css";

import BookingInfo from "./components/layout/BookingInfo";
import CarShowcase from "./components/layout/CarShowcase";
import CommitmentSection from "./components/layout/Commitment";
import Footer from "./components/layout/Footer";
import FrequentlyAQ from "./components/layout/FrequentlyAQ";
import { GoogleReCaptchaProvider } from "react-google-recaptcha-v3";
import Header from "./components/layout/Header";
import { HelmetProvider } from "react-helmet-async";
import HeroSection from "./components/layout/HeroSection";
import { Loading } from "./components/ui/loading";
import { MantineProvider } from "@mantine/core";
import MapInfo from "./components/layout/MapInfo";
import SEO from "./components/SEO/SEO";
import SearchForm from "./components/search/SearchForm";
import dayjs from "dayjs";
import timezone from "dayjs/plugin/timezone";
import utc from "dayjs/plugin/utc";

dayjs.extend(utc);
dayjs.extend(timezone);

function App() {
  return (
    <GoogleReCaptchaProvider reCaptchaKey={import.meta.env.VITE_RECAPTCHA_SITE_KEY}>
      <HelmetProvider>
        <MantineProvider>
          <div className="overflow-x-hidden">
            <SEO />
            <Header />
            {/* Component Loading */}
            <Loading />

            <main className="pt-20 mb-32">
              <div className="h-full w-full">
                {/* Hero Section */}
                <HeroSection>
                  <SearchForm />
                </HeroSection>
                <CommitmentSection />
                <BookingInfo />
                <CarShowcase />
                <MapInfo />
                <FrequentlyAQ />
              </div>
            </main>
            <div className="relative -mt-[8.5rem] px-0 lg:px-[5%] bg-black">
              <Footer />
            </div>
          </div>
        </MantineProvider>
      </HelmetProvider>
    </GoogleReCaptchaProvider>
  );
}

export default App;
