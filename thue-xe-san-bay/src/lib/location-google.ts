import axios from 'axios'

const apiKey = 'YOUR_GOOGLE_MAPS_API_KEY'

const getTravelTimeGoogleMaps = async (origin: string, destination: string) => {
  try {
    // Gọi API Directions của Google Maps
    const url = `https://maps.googleapis.com/maps/api/directions/json?origin=${origin}&destination=${destination}&key=${apiKey}`

    const response = await axios.get(url)

    if (response.data.routes.length === 0) {
      throw new Error('Không tìm thấy lộ trình')
    }

    const duration = response.data.routes[0].legs[0].duration.text // Thời gian di chuyển trả về dưới dạng text (ví dụ: "10 mins")

    return duration // Trả về thời gian di chuyển
  } catch (error) {
    console.error('Lỗi khi tính toán thời gian di chuyển:', error)
    return null
  }
}

// Thay bằng API Key của bạn
getTravelTimeGoogleMaps('Hanoi, Vietnam', 'Ho Chi Minh City, Vietnam')
  .then((time) => console.log('Thời gian di chuyển: ', time))
  .catch((err) => console.error(err))
