import * as z from "zod";

import i18n from "../../i18n";
import { hasTodayAvailableTime, getFirstAvailableTime } from "../../utils/time-utils";

// <PERSON><PERSON><PERSON> c<PERSON>c thông báo lỗi từ i18n
const errorMessages = {
  pickupRequired: i18n.t("validation.pickupRequired", "Vui lòng chọn điểm đón"),
  destinationRequired: i18n.t(
    "validation.destinationRequired",
    "<PERSON>ui lòng chọn điểm đến"
  ),
  dateRequired: i18n.t("validation.dateRequired", "<PERSON>ui lòng chọn ngày đi"),
  timeRequired: i18n.t("validation.timeRequired", "Vui lòng chọn giờ đi"),
};

/**
 * Schema validation for airport car search form
 */
export const searchFormSchema = z.object({
  pickup: z.string().min(1, errorMessages.pickupRequired),
  destination: z.string().min(1, errorMessages.destinationRequired),
  date: z.date({
    required_error: errorMessages.dateRequired,
  }),
  time: z.string()
    .min(1, errorMessages.timeRequired)
    .refine((val) => val && val.match(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/), {
      message: errorMessages.timeRequired
    }),
});

/**
 * Type generated from the search form schema
 */
export type SearchFormValues = z.infer<typeof searchFormSchema>;

// Tính toán ngày mặc định
const getDefaultDate = () => {
  // Kiểm tra xem ngày hôm nay còn giờ hợp lệ không
  const hasTimeToday = hasTodayAvailableTime();

  if (hasTimeToday) {
    // Nếu còn giờ hợp lệ, chọn ngày hôm nay
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return today;
  } else {
    // Nếu không còn giờ hợp lệ, chọn ngày mai
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    return tomorrow;
  }
};

// Lấy ngày mặc định
const defaultDate = getDefaultDate();

// Tính toán giờ mặc định dựa vào ngày đã chọn
const defaultTime = getFirstAvailableTime(defaultDate);

/**
 * Default values for search form
 */
export const searchFormDefaultValues: Partial<SearchFormValues> = {
  pickup: undefined,
  destination: undefined,
  date: defaultDate,
  time: defaultTime,
};

// /**
//  * Returns available pickup options based on selected destination
//  */
// export const getAvailablePickupOptions = (destination: string) => {
//   if (destination === "hoi-an" || destination === "hue") {
//     return ["dad"];
//   } else if (destination === "dad") {
//     return ["hoi-an", "hue"];
//   }
//   return ["dad", "hoi-an", "hue"];
// };

// /**
//  * Returns available destination options based on selected pickup
//  */
// export const getAvailableDestinationOptions = (pickup: string) => {
//   if (pickup === "dad") {
//     return ["hoi-an", "hue"];
//   } else if (pickup === "hoi-an" || pickup === "hue") {
//     return ["dad"];
//   }
//   return ["dad", "hoi-an", "hue"];
// };
