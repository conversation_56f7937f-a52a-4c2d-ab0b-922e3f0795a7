import * as z from "zod";

import { isValidPhoneNumber, parsePhoneNumberWithError } from 'libphonenumber-js';

import i18next from "i18next";

/**
 * Custom phone number validation function using libphonenumber-js
 * 
 * This function validates phone numbers with the following criteria:
 * - Must be in international format with country code (e.g., +84987654321)
 * - Must be a valid phone number for the detected country
 * - Must be either mobile, fixed line, or fixed line or mobile type
 * - Excludes premium rate, toll-free, and other special number types
 * 
 * Examples of valid formats:
 * - Vietnam: +84987654321, +8412345678
 * - US: +12345678900
 * - UK: +447123456789
 * 
 * @param phone - The phone number string to validate
 * @returns boolean indicating if the phone number is valid for booking purposes
 */
const isPhoneValid = (phone: string): boolean => {
  if (!phone || typeof phone !== 'string') {
    return false;
  }

  try {
    // First check with basic validation
    if (!isValidPhoneNumber(phone)) {
      return false;
    }

    // Parse the phone number to get more details
    const phoneNumber = parsePhoneNumberWithError(phone);
    
    // Additional checks for business logic
    if (!phoneNumber) {
      return false;
    }

    // Must be a valid number with country code
    if (!phoneNumber.country || !phoneNumber.nationalNumber) {
      return false;
    }

    // Must be a mobile or fixed line (not premium rate, etc.)
    const phoneType = phoneNumber.getType();
    const validTypes = ['MOBILE', 'FIXED_LINE', 'FIXED_LINE_OR_MOBILE'];
    
    return phoneType ? validTypes.includes(phoneType) : true; // Allow if type is unknown
  } catch {
    // If parsing fails, the number is invalid
    return false;
  }
};

/**
 * Tạo schema validation cho booking form
 * Function này tạo schema mới mỗi khi được gọi, sử dụng ngôn ngữ hiện tại để hiển thị messages
 */
export const createBookingFormSchema = () => {
  /**
   * Schema validation for booking form
   */
  return z.object({
    fullName: z.string().min(1, {
      message: i18next.t("validation.fullNameRequired", "Please enter your full name")
    }),

    // Số điện thoại (bắt buộc) - Enhanced validation with libphonenumber-js
    phoneNumber: z.string()
      .min(1, {
        message: i18next.t("validation.phoneRequired", "Phone number is required")
      })
      .refine((phone) => {
        return isPhoneValid(phone);
      }, {
        message: i18next.t("validation.invalidPhone", "Please enter a valid phone number with country code")
      }),

    // Email (tùy chọn)
    email: z.string()
      .email({
        message: i18next.t("validation.invalidEmail", "Invalid email address")
      })
      .optional()
      .or(z.literal('')), // Cho phép chuỗi rỗng

    // Thông tin chung
    notes: z.string().optional(),
    flightCode: z.string().optional(),
    pickupCode: z.string(),
    destinationCode: z.string(),
    pickupDateTime: z.string(),

    // Policy fields
    acceptTerms: z.boolean().refine(val => val === true, {
      message: i18next.t("validation.acceptTermsRequired", "You must agree to the General Terms and Payment Terms")
    }),
  });
};

// Tạo schema mặc định khi import
export const bookingFormSchema = createBookingFormSchema();

/**
 * Type generated from the booking form schema
 */
export type BookingFormValues = z.infer<typeof bookingFormSchema>;

/**
 * Default values for booking form
 */
export const bookingFormDefaultValues: Partial<BookingFormValues> = {
  fullName: "",
  phoneNumber: "",
  email: "",
  notes: "",
  pickupCode: "",
  destinationCode: "",
  pickupDateTime: "",
};