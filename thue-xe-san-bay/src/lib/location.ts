import axios from "axios";

// Hàm tính thời gian di chuyển giữa hai địa chỉ (sử dụng OSRM)
export const getTravelTimeOSM = async (
  originCoords: [string, string],
  destinationCoords: [string, string]
) => {
  try {
    // Sử dụng OSRM để tính toán thời gian di chuyển
    const url = `http://router.project-osrm.org/route/v1/driving/${originCoords[0]},${originCoords[1]};${destinationCoords[0]},${destinationCoords[1]}?overview=false&geometries=geojson`;

    const response = await axios.get(url);

    if (response.data.routes.length === 0) {
      throw new Error("Không tìm thấy lộ trình");
    }

    const duration = response.data.routes[0].duration / 60; // Chuyển thời gian từ giây sang phút
    return duration.toFixed(2);
  } catch (error) {
    console.error("Lỗi khi tính toán thời gian di chuyển:", error);
    return null;
  }
};
