import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Xây dựng URL mới với các tham số hiện tại được giữ nguyên
 * @param basePath Đường dẫn cơ bản
 * @param includeEn Flag quyết định có thêm /en vào đường dẫn hay không
 * @returns URL mới với các tham số hiện tại
 */
export function buildUrlWithParams(
  basePath: string,
  includeEn: boolean
): string {
  // Lấy các tham số hiện tại từ URL
  const currentParams = new URLSearchParams(window.location.search);

  // Xây dựng đường dẫn mới
  const newPath = includeEn ? `${basePath}/en` : basePath;

  // Nếu không có tham số, tr<PERSON> về đường dẫn mới
  if (!currentParams.toString()) {
    return newPath;
  }

  // Nếu có tham số, thêm vào đường dẫn mới
  return `${newPath}?${currentParams.toString()}`;
}

export function convertMinutesToHours(minutes: number) {
  const hrs = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return { hour: hrs, mins: mins };
}
