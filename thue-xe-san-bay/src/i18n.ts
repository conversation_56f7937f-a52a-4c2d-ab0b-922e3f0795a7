import LanguageDetector from "i18next-browser-languagedetector";
import i18n from "i18next";
import { initReactI18next } from "react-i18next";

// Tạo object chứa các ngôn ngữ trực tiếp
const resources = {
  en: {
    translation: {
      title: "Car Rental at Airport",
      welcome: "Welcome to Airport Car Rental Service",
      language: "Language",
      searching: "Searching for available cars...",
      loading: {
        searchingVehicles: "Searching for suitable vehicles...",
      },
      hero: {
        title: "Redefine your elegance and comfort!",
        description:
          "Green Future's airport transfer service offers a premium experience - on time - no hidden fees. Book in just a few clicks, and our professional drivers will be ready to pick you up as soon as you land.",
      },
      nav: {
        bookCar: "Book a car",
        buyUsedCar: "Buy certified used cars",
        airportCars: "Airport Car Rental",
        about: "About us",
        news: "News",
      },
      searchForm: {
        title: "Airport Car Rental Service",
        pickup: "Pickup Place",
        destination: "Destination",
        selectLocation: "Select location",
        noLocations: "No locations available",
        swapLocations: "Swap pickup and destination",
        search: "Find Suitable Vehicle",
        byJourney: "By Itinerary",
        byHour: "By Hour Package",
        airportPickup: "Airport Pickup",
        enterLocation: "Please enter a location to search",
        searching: "Searching...",
      },
      dateTimePicker: {
        selectDate: "Departure Date",
        dateFormat: "DD/MM/YYYY",
        time: "Time",
        holidaySurcharge: "Holiday dates, additional surcharge applies",
        selectedDay: "Selected day",
        holidaySelected: "Holiday selected",
        today: "Today",
        tomorrow: "Tomorrow",
        selectTime: "Select time",
        noTimeAvailable: "No time slots available",
      },
      validation: {
        pickupRequired: "Please select a pickup point",
        destinationRequired: "Please select a destination",
        dateRequired: "Please select a date",
        timeRequired: "Please select a time",
        durationRequired: "Please select a duration",
        fullNameRequired: "Please enter your full name",
        phoneRequired: "Phone number is required",
        invalidPhone:
          "Please enter a valid phone number with country code (e.g., ****** 567 8900)",
        invalidEmail: "Invalid email address",
        acceptTermsRequired:
          "You must agree to the General Terms and Payment Terms",
        selectFromDropdown: "Please select a location from the dropdown",
        locationNotFound: "Location not found",
        requiredField: "This field is required",
      },
      booking: {
        selectAnotherCar: "Select another car",
        bookingTitle: "Book a car",
        fullName: "Full name",
        enterFullName: "Enter your full name",
        phoneNumber: "Phone number",
        enterPhoneNumber: "Enter your phone number",
        enterPhoneNumberIntl: "Enter phone number (e.g. +1...)",
        noPhoneNumber: "Don't have a phone number?",
        optional: "(Optional)",
        switchToEmail: "Enter email",
        email: "Email",
        enterEmail: "Enter your email address",
        usePhoneNumber: "Switch to your phone number",
        switchToPhone: "Enter phone number",
        flightNumber: "Flight number",
        enterFlightNumber: "Enter your flight number",
        flightCode: "Flight number",
        enterFlightCode: "Enter your flight number",
        pickupHall: "Pickup hall",
        enterPickupHall: "Pickup location at the airport",
        notes: "Message",
        enterNotes: "Leave a message for us",
        processing: "Processing...",
        bookCar: "Airport Car Rental Service",
        termsAgree:
          "I confirm that I have read, understood, and agree to the {{generalTermsLink}} and {{paymentTermsLink}} of Green Future.",
        generalTerms: "General Terms",
        paymentTerms: "Payment Terms",
        duration: "Rental Duration",
        selectDuration: "Select duration",
        continue: "Continue",
        "duration0.5": "30 minutes",
        duration1: "1 hour",
        duration2: "2 hours",
        duration4: "4 hours",
        duration6: "6 hours",
        duration8: "8 hours",
        duration12: "12 hours",
        duration24: "24 hours",
        terms: "Payment Terms",
        dataAgree:
          "I consent to provide my personal information in accordance with",
        dataPolicy: "Green Future 's Personal Data Sharing Terms",
        of: "of",
        bookNow: "Book Now",
        companyName: "Green Future",
        bookingSuccess: "Booking Successful",
        bookingFailed: "Booking Failed",
        tryAgain: "Please try again",
        contactSupport: "Contact Support",
      },
      carSelection: {
        title: "Choose Your Vehicle",
        noVehicles: "No suitable vehicles at this location",
      },
      carCard: {
        type: "D-SUV",
        seats: "5 Seats",
        trunk: "Trunk 350L",
        model: "Model",
        price: "Price",
        features: {
          seats: "4 Seats",
          ac: "Automatic A/C",
          sunroof: "Sunroof",
          wifi: "Free WiFi",
          water: "Complimentary water",
          driver: "Dedicated and professional driver",
          airbags:
            "Absolute peace of mind with protective system of 11 airbags",
          charging: "Free phone and laptop charging (USB-C)",
          vf8: {
            type: "D-SUV",
            seats: "5 seats",
            trunk: "Spacious trunk",
          },
          vf9: {
            type: "E-SUV",
            seats: "6-7 seats",
            trunk: "Extra spacious trunk",
            seats_heating: "Relax with heated and massaging seat system",
          },
        },
        selectButton: "Select",
        selectButtonWithModel: "Select {{model}}",
      },
      tripInfo: {
        title: "Trip Information",
        pickupPoint: "Pickup Point",
        destinationPoint: "Destination Point",
        paymentAmount: "Payment Amount",
        priceIncludesVAT: "Price includes VAT",
        estTimeDuration: "Estimated duration",
        rentalDuration: "Rental duration",
        from: "from",
        additionalCharges:
          "* The actual payment may include additional fees based on the actual travel",
        extraTime: "Extra time",
        extraFee: "Extra fees",
        minutes: "minutes",
        free: "Free",
        hour_one: " hour",
        hour_other: " hours",
        hour: "{{ count }} hour",
        hours: "hours",
        minute: "minute",
        hourShort: "h",
        minuteShort: "m",
        movingTime: "Estimated travel time",
        extraNotes:
          "Additional $0.6/km applies if exceeding package limit, up to 10 km max",
        serviceTitle: "Select GF VIP Airport Shuttle Service",
        pickupTime: "Pickup Time",
        pickupTime2: "Pickup Time",
        distance: "Distance",
        travelTime: "Travel Time",
        additionalFees:
          "Toll fees and any additional charges (if applicable) will be calculated separately according to the route.",
        surcharges: {
          doorOpeningFee: "Opening fare",
          doorOpeningFeeNote: "Applied for the first 3km",
          km4to30: "From the 4th km to the 30th km",
          km31plus: "From the 31st km onward",
          pricePerKm: "${{price}}/km",
          pricePerMinutes: "${{price}}/30 minutes",
        },
      },
      showCase: {
        title: "Available Car Models\nLuxury VF8 & VF9",
        seats: "{{count}} Seats",
        trunkCapacity: "Trunk capacity {{count}}L",
      },
      infos: {
        title: "Airport transfers have never been this fast and seamless!",
        info_key: {
          title: "Enter Your Flight & Destination Details",
          content:
            "Tell us where you need to be picked up and your travel plans",
        },
        selection_key: {
          title: "Choose the Perfect Ride",
          content:
            "From standard to VIP, family cars to group transport—pick what suits you best",
        },
        accept_key: {
          title: "Confirmation",
          content:
            "Our support team will call to confirm your details and ensure your ride is ready.",
        },
        enjoy_key: {
          title: "Enjoy the Journey",
          content:
            "A professional driver will arrive on time, ready to make your trip effortless",
        },
      },
      commitment: {
        title: "Your favorite airport",
        title2line: "car rental service",
        features: [
          {
            number: "01",
            title: "Precise\nTo the minute",
            description:
              "Plane lands, car waiting\nTime is precious, not a second wasted!",
          },
          {
            number: "02",
            title: "Premium cars,\nComfortable seats",
            description:
              "Cool air conditioning, comfortable seats,\nA light journey, no worries.",
          },
          {
            number: "03",
            title: "Professional,\nCourteous drivers",
            description:
              "Long distances are no problem with safe driving,\nCourteous, dedicated, fluent communication!",
          },
          {
            number: "04",
            title: "Support\n24/7",
            description:
              "Transparent fees, no hidden costs.\nRelax in the car, travel with peace of mind.",
          },
        ],
      },
      faq: {
        title: "Frequently Asked Questions",
        items: [
          {
            question: "Does Green Future operate 24/7?",
            answer:
              "Yes! Our airport transfer service is available from 7AM to 10h30 PM everyday, ensuring the best service anytime, anywhere.",
          },
          {
            question: "Can I modify my schedule after booking a ride?",
            answer:
              "Absolutely! You can contact our customer service to adjust your schedule free of charge up to 3 hours before your scheduled pickup time.",
          },
          {
            question: "Do drivers assist with luggage?",
            answer:
              "Yes! Our drivers are always ready to help you with your luggage.",
          },
          {
            question: "Can I book a ride for someone else?",
            answer:
              "Yes! Simply provide the passenger's details when booking, and our driver will contact them directly upon arrival.",
          },
          {
            question: "Can I cancel my booked ride?",
            answer:
              "Yes! You can cancel free of charge up to 3 hours before your scheduled pickup time. Cancellations made after this period will be subject to our cancellation policy.",
          },
        ],
      },
      mapInfo: {
        title: "Available at",
        mapAlt: "map-info",
        airports: {
          prefix: "International Airport",
          danang: {
            city: "Da Nang",
            location: "Da Nang",
          },
          hanoi: {
            city: "Ha Noi",
            location: "Noi Bai",
          },
          hochiminh: {
            city: "Ho Chi Minh City",
            location: "Tan Son Nhat",
          },
        },
        bookNow: "Book Now",
        comingSoon: "Coming soon",
      },
      formSuccess: {
        title: "Booking successful",
        message:
          "Thank you for trusting and using Green Future's servicies. We have received your trip information and will contact you soonest.",
        message2:
          "We have received your booking information and will contact you soonest.",
        hotlineText: "For any inquiries, please contact our HOTLINE at",
        hotlineNumber: "1900 1877",
        closeButton: "I understand",
        moreButton: "View more services from Green Future",
      },
      formWarning: {
        title: "Distance exceeds the limit",
        message:
          "The distance you selected exceeds 130 km from the starting point. Please contact our Customer Service department for support.",
        contactText: "Contact HOTLINE",
        contactNumber: "1900 1877",
        closeButton: "Close",
        contactButton: "Call now",
        imageAlt: "Distance Warning Image",
      },
      footer: {
        companyName: "GREEN FUTURE SERVICES AND TRADING JOINT STOCK COMPANY",
        businessInfo:
          "Business Registration: 0110771284. Issued by Hanoi Authority for Planning and Investment. The first registration dated on 02 July 2024. The sixth registration for change dated on 28 February 2025.",
        address:
          "Address: Symphony Office Building, Chu Huy Mân Street, Vinhomes Riverside Urban Area, Phuc Loi Ward, Long Bien District, Hanoi City, Vietnam",
        copyright: "© 2025 Green Future. All rights reserved.",
        carRental: "Car Rental",
        shortTerm: "Short Term",
        longTerm: "Long Term",
        business: "Business",
        introduction: "Introduction",
        aboutUs: "About us",
        services: "Services",
        news: "News",
        contact: "Contact",
        termsConditions: "Terms & Conditions",
      },
      ui: {
        loading: "Loading...",
        error: "An error occurred",
        success: "Success!",
        required: "Required",
        back: "Back",
        next: "Next",
        cancel: "Cancel",
        save: "Save",
        submit: "Submit",
        select: "Select",
        search: "Search",
        noResults: "No results found",
        close: "Close",
        requiredMark: "*",
      },
    },
  },
  vi: {
    translation: {
      title: "Thuê Xe Tại Sân Bay",
      welcome: "Chào mừng đến với dịch vụ thuê xe sân bay",
      language: "Ngôn ngữ",
      searching: "Đang tìm xe phù hợp...",
      loading: {
        searchingVehicles: "Đang tìm xe phù hợp...",
      },
      hero: {
        title: "Đẳng cấp vươn tầm\n Thoải mái chọn hành trình",
        description:
          "Dịch vụ đặt xe sân bay Green Future mang đến trải nghiệm cao cấp - đúng giờ - không lo phát sinh phí ẩn. Đặt trước chỉ với vài cú nhấp chuột, tài xế chuyên nghiệp luôn sẵn sàng chờ bạn ngay khi hạ cánh.",
      },
      nav: {
        bookCar: "Đặt xe",
        buyUsedCar: "Mua xe cũ chính hãng",
        airportCars: "Thuê Xe Sân Bay",
        about: "Giới thiệu",
        news: "Tin tức",
      },
      searchForm: {
        title: "Đặt dịch vụ",
        pickup: "Điểm đón",
        destination: "Điểm đến",
        selectLocation: "Chọn địa điểm",
        noLocations: "Không có địa điểm",
        swapLocations: "Hoán đổi điểm đón và điểm đến",
        search: "Tìm xe phù hợp",
        byJourney: "Theo hành trình",
        byHour: "Theo gói giờ",
        airportPickup: "Đón tại sân bay",
        enterLocation: "Vui lòng nhập để tìm kiếm",
        searching: "Đang tìm kiếm...",
      },
      dateTimePicker: {
        selectDate: "Chọn thời gian đón",
        dateFormat: "DD/MM/YYYY",
        time: "Giờ",
        holidaySurcharge: "Ngày lễ, giá có thêm phụ phí",
        selectedDay: "Ngày đã chọn",
        holidaySelected: "Ngày lễ đã chọn",
        today: "Hôm nay",
        tomorrow: "Ngày mai",
        selectTime: "Chọn giờ",
        noTimeAvailable: "Không có khung giờ nào khả dụng",
      },
      validation: {
        pickupRequired: "Vui lòng chọn điểm đón",
        destinationRequired: "Vui lòng chọn điểm đến",
        dateRequired: "Vui lòng chọn ngày đi",
        timeRequired: "Vui lòng chọn giờ đi",
        durationRequired: "Vui lòng chọn thời gian",
        fullNameRequired: "Vui lòng nhập họ và tên",
        phoneRequired: "Vui lòng nhập số điện thoại",
        invalidPhone:
          "Vui lòng nhập số điện thoại hợp lệ với mã quốc gia (VD: +84 987 654 321)",
        invalidEmail: "Email không hợp lệ",
        acceptTermsRequired:
          "Vui lòng đồng ý với Điều khoản chung và Điều khoản thanh toán",
        selectFromDropdown: "Vui lòng chọn địa điểm từ danh sách",
        locationNotFound: "Không tìm thấy địa điểm",
        requiredField: "Trường này là bắt buộc",
      },
      booking: {
        selectAnotherCar: "Chọn xe khác",
        bookingTitle: "Đặt dịch vụ",
        fullName: "Họ và tên",
        enterFullName: "Nhập họ và tên của bạn",
        phoneNumber: "Số điện thoại",
        enterPhoneNumber: "Nhập số điện thoại của bạn",
        enterPhoneNumberIntl: "Nhập số điện thoại (VD: +84...)",
        noPhoneNumber: "Không có số điện thoại?",
        optional: "",
        switchToEmail: "Chuyển qua dùng địa chỉ email",
        email: "Email",
        enterEmail: "Nhập địa chỉ email của bạn",
        usePhoneNumber: "Muốn sử dụng số điện thoại?",
        switchToPhone: "Chuyển qua dùng số điện thoại",
        flightNumber: "Mã chuyến bay",
        enterFlightNumber: "Nhập mã chuyến bay của bạn",
        flightCode: "Mã chuyến bay",
        enterFlightCode: "Nhập mã chuyến bay của bạn",
        pickupHall: "Sảnh đón",
        enterPickupHall: "Địa điểm đón tại sân bay",
        notes: "Lời nhắn",
        enterNotes: "Nhập ghi chú hoặc yêu cầu đặc biệt",
        processing: "Đang xử lý...",
        bookCar: "Thanh toán",
        termsAgree:
          "Tôi xác nhận đã đọc hiểu và đồng ý với {{generalTermsLink}} và {{paymentTermsLink}} của Green Future.",
        generalTerms: "Điều khoản chung",
        paymentTerms: "Điều khoản thanh toán",
        duration: "Thời gian thuê",
        selectDuration: "Chọn thời gian thuê",
        continue: "Tiếp tục",
        "duration0.5": "30 phút",
        duration1: "1 giờ",
        duration2: "2 giờ",
        duration4: "4 giờ",
        duration6: "6 giờ",
        duration8: "8 giờ",
        duration12: "12 giờ",
        duration24: "24 giờ",
        terms: "Điều khoản chung",
        dataAgree: "Tôi đồng ý để lại thông tin cá nhân theo",
        dataPolicy: "Điều khoản chia sẻ dữ liệu cá nhân",
        of: "của",
        bookNow: "Đặt Ngay",
        companyName: "Green Future",
        bookingSuccess: "Đặt xe thành công",
        bookingFailed: "Đặt xe thất bại",
        tryAgain: "Vui lòng thử lại",
        contactSupport: "Liên hệ hỗ trợ",
      },
      carSelection: {
        title: "Lựa chọn dòng xe",
        noVehicles: "Không có xe phù hợp tại địa điểm này",
      },
      carCard: {
        type: "D-SUV",
        seats: "5 Chỗ",
        trunk: "Dung tích cốp 350L",
        model: "Mẫu xe",
        price: "Giá",
        features: {
          seats: "4 Chỗ",
          ac: "Điều hòa tự động",
          sunroof: "Cửa sunroof",
          wifi: "WiFi miễn phí",
          water: "Nước uống miễn phí",
          driver: "Tài xế tận tâm, chuyên nghiệp",
          airbags: "An tâm tuyệt đối với hệ thống bảo vệ 11 túi khí",
          charging: "Sạc điện thoại, thiết bị di động miễn phí",
          vf8: {
            type: "D-SUV",
            seats: "5 chỗ",
            trunk: "Cốp xe rộng rãi",
          },
          vf9: {
            type: "E-SUV",
            seats: "6 - 7 chỗ",
            trunk: "Cốp xe siêu rộng rãi",
            seats_heating: "Thư giãn cùng hệ thống ghế sưởi, massage",
          },
        },
        selectButton: "Chọn",
        selectButtonWithModel: "Chọn dịch vụ",
      },
      tripInfo: {
        title: "Hành trình của bạn",
        pickupPoint: "Điểm đón",
        destinationPoint: "Điểm trả",
        paymentAmount: "Số tiền thanh toán",
        priceIncludesVAT: "Giá đã bao gồm VAT",
        estTimeDuration: "Thời gian di chuyển dự kiến",
        rentalDuration: "Thời gian thuê",
        from: "từ",
        additionalCharges:
          "Phí cầu đường và các khoản phát sinh (nếu có) sẽ được tính riêng theo lộ trình.",
        extraTime: "Thời gian phát sinh",
        extraFee: "Phụ phí",
        minutes: "phút",
        free: "Miễn phí",
        hour: "{{ count }} giờ",
        hour_one: " giờ",
        hour_other: " giờ",
        hours: "giờ",
        minute: "phút",
        hourShort: "h",
        minuteShort: "m",
        movingTime: "Thời gian di chuyển dự kiến",
        extraNotes:
          "Nếu phát sinh vượt số km tối đa theo gói di chuyển, quý khách vui lòng thanh toán phụ phí 15.000đ/km. Số km vượt quá không lớn hơn 10km.",
        serviceTitle: "Lựa chọn dịch vụ đưa đón sân bay GF VIP",
        pickupTime: "Thời gian đón",
        pickupTime2: "Giờ đón",
        distance: "Quãng đường",
        travelTime: "Thời gian di chuyển",
        additionalFees:
          "Phí cầu đường và các khoản phát sinh (nếu có) sẽ được tính riêng theo lộ trình.",
        surcharges: {
          doorOpeningFee: "Giá mở cửa",
          doorOpeningFeeNote: "Áp dụng cho 3km đầu tiên",
          km4to30: "Từ km thứ 4 đến km thứ 30",
          km31plus: "Từ km thứ 31 trở đi",
          pricePerKm: "{{price}}đ/km",
          pricePerMinutes: "${{price}}/30 phút",
        },
      },
      showCase: {
        title: "Dòng Xe Hiện Có\nLuxury VF8 & VF9",
        seats: "{{count}} Chỗ",
        trunkCapacity: "Dung tích cốp {{count}}L",
      },
      commitment: {
        title: "Dịch vụ thuê xe sân bay",
        title2line: "yêu thích của bạn",
        features: [
          {
            number: "01",
            title: "Dịch vụ đáng tin cậy",
            description:
              "Máy bay đáp xuống, xe chờ sẵn\nThời gian quý giá, chẳng phí một giây!",
          },
          {
            number: "02",
            title: "Giá trọn gói,\nKhông phí ẩn",
            description:
              "Biết giá trước, không lo phát sinh - đặt xe với mức giá cố định, không bị phụ phí giờ cao điểm hay đường xa.",
          },
          {
            number: "03",
            title: "Trải nghiệm đẳng cấp, thoải mái",
            description:
              "Đa dạng các dòng xe điện - không gian rộng rãi\n Vận hành êm ái, tiện nghi đầy đủ cho chuyến đi thoải mái nhất.",
          },
          {
            number: "04",
            title: "Tài xế chuyên nghiệp, lịch sự",
            description:
              "Tài xế thân thiện, được đào tạo bài bản, phục vụ tận tâm, giao tiếp tốt ngoại ngữ\n Sẵn sàng hỗ trợ khách hàng chu đáo trong suốt hành trình.",
          },
        ],
      },
      faq: {
        title: "Câu hỏi thường gặp",
        items: [
          {
            question: "Dịch vụ của Green Future có hoạt động 24/7 không?",
            answer:
              "Dịch vụ đưa đón sân bay của chúng tôi luôn sẵn sàng  từ 7h đến 22h30 mỗi ngày để phục vụ quý khách tốt nhất.",
          },
          {
            question: "Tôi có thể thay đổi lịch trình sau khi đặt xe không?",
            answer:
              "Hoàn toàn được! Bạn có thể liên hệ CSKH để thay đổi miễn phí trước 3 giờ so với giờ đón.",
          },
          {
            question: "Tài xế có hỗ trợ giúp hành lý không?",
            answer: "Có! Tài xế của chúng tôi luôn sẵn sàng hỗ trợ bạn.",
          },
          {
            question: "Tôi có thể đặt xe cho người khác không?",
            answer:
              "Được! Bạn chỉ cần nhập thông tin hành khách khi đặt xe, tài xế sẽ liên hệ trực tiếp với người đó khi đến đón.",
          },
          {
            question: "Tôi có thể hủy chuyến xe đã đặt không?",
            answer:
              "Có! Bạn có thể hủy miễn phí trước 3 giờ so với giờ đón. Nếu hủy sau thời gian này, sẽ áp dụng phí theo chính sách của chúng tôi.",
          },
        ],
      },
      infos: {
        title: "Di chuyển từ sân bay chưa bao giờ\nnhanh & tiện lợi đến thế!",
        info_key: {
          title: "Nhập thông tin chuyến bay & điểm đến",
          content:
            "Cho chúng tôi biết bạn cần đón ở đâu và dự định di chuyển tới đâu",
        },
        selection_key: {
          title: "Chọn loại xe phù hợp",
          content:
            "Từ xe tiêu chuẩn đến VIP, xe gia đình hay nhóm bạn, thoải mái lựa chọn.",
        },
        accept_key: {
          title: "Xác nhận chuyến đi",
          content:
            "Ngay sau khi đặt, tổng đài viên sẽ gọi để xác nhận thông tin và đảm bảo xe luôn sẵn sàng.",
        },
        enjoy_key: {
          title: "Tận hưởng hành trình",
          content:
            "Tài xế chuyên nghiệp sẽ đến đón bạn đúng giờ, sẵn sàng phục vụ chuyến đi suôn sẻ!",
        },
      },
      mapInfo: {
        title: "Sẵn sàng phục vụ tại",
        mapAlt: "bản đồ thông tin",
        airports: {
          prefix: "Sân bay quốc tế",
          danang: {
            city: "Đà Nẵng",
            location: "Đà Nẵng",
          },
          hanoi: {
            city: "Hà Nội",
            location: "Nội Bài",
          },
          hochiminh: {
            city: "Hồ Chí Minh",
            location: "Tân Sơn Nhất",
          },
        },
        bookNow: "Đặt xe",
        comingSoon: "Coming soon",
      },
      formSuccess: {
        title: "Chúng tôi đã nhận thông tin",
        message: "Cảm ơn Quý khách đã gửi thông tin.",
        message2:
          "GF đã nhận được thông tin đăng ký của Quý khách và chúng tôi sẽ liên hệ trong thời gian sớm nhất",
        hotlineText: "Mọi thắc mắc xin liên hệ HOTLINE",
        hotlineNumber: "1900 1877",
        closeButton: "Đóng thông báo",
        moreButton: "Xem thêm dịch vụ của Green Future",
      },
      formWarning: {
        title: "Khoảng cách vượt giới hạn",
        message:
          "Khoảng cách bạn chọn vượt quá 130km so với điểm xuất phát. Vui lòng liên hệ bộ phận CSKH để được hỗ trợ tốt nhất.",
        contactText: "Liên hệ HOTLINE",
        contactNumber: "1900 1877",
        closeButton: "Đóng",
        contactButton: "Gọi ngay",
        imageAlt: "Hình ảnh cảnh báo khoảng cách",
      },
      footer: {
        companyName: "CÔNG TY CỔ PHẦN THƯƠNG MẠI VÀ DỊCH VỤ GREEN FUTURE",
        businessInfo:
          "MST/MSDN: 0110771284 do Sở KHĐT TP Hà Nội cấp lần 06 ngày 28/02/2025",
        address:
          "Địa chỉ: Tòa văn phòng Symphony, Đường Chu Huy Mân, Khu đô thị Vinhomes Riverside, Phường Phúc Lợi, Quận Long Biên, Thành phố Hà Nội, Việt Nam",
        copyright: "© 2025 Green Future. All rights reserved.",
        carRental: "Đặt xe",
        shortTerm: "Ngắn hạn",
        longTerm: "Dài hạn",
        business: "Doanh nghiệp",
        introduction: "Giới thiệu",
        aboutUs: "Về chúng tôi",
        services: "Dịch vụ",
        news: "Tin tức",
        contact: "Liên hệ",
        termsConditions: "Điều khoản sử dụng",
      },
      ui: {
        loading: "Đang tải...",
        error: "Đã xảy ra lỗi",
        success: "Thành công!",
        required: "Bắt buộc",
        back: "Quay lại",
        next: "Tiếp tục",
        cancel: "Hủy",
        save: "Lưu",
        submit: "Gửi",
        select: "Chọn",
        search: "Tìm kiếm",
        noResults: "Không tìm thấy kết quả",
        close: "Đóng",
        requiredMark: "*",
      },
    },
  },
};

// Cấu hình Language Detector
const languageDetector = new LanguageDetector();
languageDetector.addDetector({
  name: "pathDetector",
  lookup() {
    // Kiểm tra URL có chứa /en/ không
    const path = window.location.pathname;
    const isEnglishPath = path.includes("/en") || path.endsWith("/en");
    return isEnglishPath ? "en" : "vi";
  },
  cacheUserLanguage(lng) {
    // Lưu ngôn ngữ vào localStorage
    localStorage.setItem("i18nextLng", lng);
  },
});

i18n
  .use(languageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: "vi",
    detection: {
      order: ["pathDetector", "localStorage", "navigator"],
      caches: ["localStorage"],
    },
    interpolation: {
      escapeValue: false,
    },
  });

export default i18n;
