import { isToday } from "date-fns";

/**
 * <PERSON><PERSON><PERSON> tra xem hiện tại có phải sau 19h30 không
 * Nếu sau 19h30, không cho phép đặt xe cho ngày hôm nay nữa
 */
export const isAfter1930 = (): boolean => {
  const now = new Date();
  const hours = now.getHours();
  const minutes = now.getMinutes();
  const result = hours > 19 || (hours === 19 && minutes >= 30);
  return result;
};

/**
 * Tính ngày mai để làm ngày mặc định và ngày bắt đầu có thể chọn
 */
export const getTomorrow = (): Date => {
  const now = new Date();
  const nextDay = new Date(now);
  nextDay.setDate(nextDay.getDate() + 1);
  // Reset time to beginning of day
  nextDay.setHours(0, 0, 0, 0);
  return nextDay;
};

/**
 * <PERSON><PERSON><PERSON> định ngày bắt đầu có thể chọn (luôn là ngày hôm nay)
 */
export const getFromDate = (): Date => {
  return new Date(); // Luôn cho phép chọn từ ngày hôm nay
};

/**
 * Tìm thời gian tối thiểu cho ngày hiện tại (3 giờ sau thời điểm hiện tại)
 * Luôn trả về một giá trị hợp lệ, không bao giờ trả về null
 */
export const getMinTimeForToday = (): string => {
  const now = new Date();
  // Hiện tại là mấy giờ
  const currentHour = now.getHours();
  const currentMinute = now.getMinutes();

  // Thêm 3 giờ vào thời gian hiện tại
  let hourAfter3Hours = currentHour + 3;

  // Nếu phút > 0, làm tròn lên 30 phút tiếp theo hoặc giờ tiếp theo
  let minute = "00";
  if (currentMinute > 0) {
    if (currentMinute < 30) {
      minute = "30";
    } else {
      hourAfter3Hours += 1;
    }
  }

  // Nếu giờ vượt quá 22h30, trả về giờ hợp lệ cuối cùng (22h30)
  if (hourAfter3Hours > 22 || (hourAfter3Hours === 22 && minute === "30")) {
    return "22:30"; // Trả về giờ hợp lệ cuối cùng
  }

  // Nếu giờ tính ra nhỏ hơn 7, đặt thành 7h sáng
  if (hourAfter3Hours < 7) {
    return "07:00";
  }

  return `${hourAfter3Hours.toString().padStart(2, "0")}:${minute}`;
};

/**
 * Kiểm tra xem ngày hôm nay còn giờ nào có thể chọn không
 */
export const hasTodayAvailableTime = (): boolean => {
  // Nếu đã quá 19h30, không cho phép đặt xe cho ngày hôm nay nữa
  if (isAfter1930()) {
    return false;
  }

  return true;
};

/**
 * Danh sách giờ có thể chọn từ 7h đến 22h30
 */
export const getAvailableTimes = (): string[] => {
  const times = [];
  // Tạo các khung giờ từ 7h sáng đến 22h30 tối
  for (let hour = 7; hour <= 22; hour++) {
    times.push(`${hour.toString().padStart(2, "0")}:00`);
    if (hour <= 22) {
      // Thêm phút 30 cho tất cả các giờ từ 7-22
      times.push(`${hour.toString().padStart(2, "0")}:30`);
    }
  }
  return times;
};

/**
 * Kiểm tra xem một giờ cụ thể có được phép chọn không
 */
export const isTimeAllowed = (timeString: string, date: Date | null): boolean => {
  if (!date) return false;

  const [hours, minutes] = timeString.split(":").map(Number);

  // Không cho phép chọn giờ ngoài khoảng 7h-22h30
  if (hours < 7 || hours > 22 || (hours === 22 && minutes > 30)) {
    return false;
  }

  // Nếu là ngày hôm nay, cần kiểm tra thêm điều kiện về giờ tối thiểu
  if (isToday(date)) {
    // Nếu đã quá 19h30, không cho phép chọn ngày hôm nay
    if (isAfter1930()) {
      return false;
    }

    const minTimeForToday = getMinTimeForToday();
    // Lấy thời gian tối thiểu cho ngày hôm nay
    // Lưu ý: getMinTimeForToday() luôn trả về giá trị, không còn trả về null nữa

    // So sánh với thời gian tối thiểu
    // minTimeForToday luôn có giá trị vì getMinTimeForToday() không còn trả về null nữa
    const [minHours, minMinutes] = minTimeForToday.split(":").map(Number);

    if (hours < minHours || (hours === minHours && minutes < minMinutes)) {
      return false;
    }
  }

  // Cho phép tất cả các mốc thời gian trong khoảng hợp lệ
  return true;
};

/**
 * Tìm giờ hợp lệ đầu tiên cho ngày đã chọn
 */
export const getFirstAvailableTime = (date: Date | null): string => {
  if (!date) return "07:00";

  // Nếu là ngày hôm nay và chưa quá 19h30
  if (isToday(date) && !isAfter1930()) {
    // getMinTimeForToday() luôn trả về giá trị, không bao giờ trả về null
    const minTime = getMinTimeForToday();
    return minTime;
  }

  // Với ngày mai trở đi, bắt đầu từ 7h sáng
  return "07:00";
};
