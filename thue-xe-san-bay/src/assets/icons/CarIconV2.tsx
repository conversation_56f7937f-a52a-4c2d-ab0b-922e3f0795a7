import React from 'react';

interface CarIconV2Props {
  className?: string;
}

const CarIconV2: React.FC<CarIconV2Props> = ({ className = "w-6 h-6" }) => {
  return (
    <svg 
      width="25" 
      height="24" 
      viewBox="0 0 25 24" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <mask id="path-1-inside-1_173450_2946" fill="white">
        <path fillRule="evenodd" clipRule="evenodd" d="M22.7 11.55C22.625 11.025 22.4 10.5 22.025 10.125C21.8 9.9 21.8 9.6 21.95 9.3C22.25 8.85 22.25 8.25 21.95 7.875C21.575 7.275 20.75 6.975 20.075 6.975C19.925 6.975 19.775 6.975 19.7 6.975C19.4 6.825 19.325 6.6 19.175 6.45C18.65 5.85 18.125 5.1 17.3 4.65C16.775 4.5 16.1 4.5 15.575 4.5C13.625 4.5 11.75 4.5 9.8 4.5C8.975 4.5 8.075 4.5 7.4 4.875C6.725 5.25 6.2 6 5.825 6.525C5.675 6.75 5.6 6.9 5.45 6.9C5.3 7.05 5.075 7.05 4.925 7.05C4.1 7.05 3.5 7.425 3.05 7.95C2.75 8.325 2.75 9 3.05 9.45C3.2 9.675 3.2 10.05 2.975 10.275C2.6 10.725 2.375 11.175 2.225 11.7C2.075 12.45 2.075 13.2 2 13.95V14.025C2 15.15 2 16.275 2.15 17.4C2.3 18.3 2.525 19.2 3.575 19.425C4.475 19.65 5.525 19.575 6.35 18.9C6.5 18.75 6.5 18.75 6.65 18.6C6.8 18.6 6.95 18.6 7.025 18.6C10.7 18.6 14.45 18.6 18.125 18.6C18.275 18.6 18.275 18.6 18.425 18.6C18.575 18.6 18.575 18.75 18.725 18.825C19.025 19.2 19.55 19.2 20.075 19.35C20.75 19.5 21.425 19.5 22.025 19.125C22.7 18.75 22.85 18 22.925 17.25C23.075 16.35 23.075 15.45 23.075 14.625C23.075 14.4 23.075 14.1 23.075 13.875C23 13.05 23 12.3 22.7 11.55ZM21.875 14.55C21.875 15.45 21.875 16.2 21.725 17.025C21.65 17.475 21.575 18 21.35 18.075C21.125 18.225 20.9 18.225 20.675 18.225C20.525 18.225 20.375 18.225 20.15 18.15C20.075 18.15 19.925 18.075 19.775 18.075C19.7 18.075 19.55 18 19.475 18C19.1 17.475 18.725 17.4 18.425 17.4H6.2L5.6 18C5.225 18.3 4.775 18.375 4.475 18.375C4.25 18.375 4.1 18.375 3.875 18.3C3.65 18.225 3.35 18.15 3.275 17.175C3.125 16.2 3.125 15.15 3.125 14.025C3.2 13.65 3.2 13.275 3.275 12.9C3.275 12.525 3.35 12.225 3.425 11.85C3.5 11.4 3.725 11.025 4.175 10.65L4.7 10.2C5.075 9.9 5.075 9.3 4.625 9.075L4.025 8.7C3.95 8.7 3.95 8.475 3.95 8.475C4.175 8.25 4.475 8.1 4.925 8.1C5.075 8.1 5.525 8.1 5.9 7.875C6.2 7.8 6.5 7.575 6.8 7.05L6.875 6.9C7.175 6.45 7.625 6 8 5.775C8.375 5.55 9.2 5.55 9.8 5.55H15.575C16.025 5.55 16.475 5.55 16.775 5.7C17.225 5.925 17.6 6.375 17.975 6.9L18.275 7.275L18.35 7.35C18.5 7.575 18.725 7.875 19.175 8.025L19.4 8.1H20.075C20.45 8.1 20.825 8.25 20.975 8.475V8.55V8.625C20.975 8.625 20.975 8.7 20.9 8.7L20.375 9C19.925 9.225 19.925 9.825 20.3 10.125L20.825 10.575C21.275 10.875 21.5 11.325 21.575 11.775V11.85V11.925C21.8 12.525 21.8 13.125 21.8 13.8V14.55H21.875Z"/>
      </mask>
      <path fillRule="evenodd" clipRule="evenodd" d="M22.7 11.55C22.625 11.025 22.4 10.5 22.025 10.125C21.8 9.9 21.8 9.6 21.95 9.3C22.25 8.85 22.25 8.25 21.95 7.875C21.575 7.275 20.75 6.975 20.075 6.975C19.925 6.975 19.775 6.975 19.7 6.975C19.4 6.825 19.325 6.6 19.175 6.45C18.65 5.85 18.125 5.1 17.3 4.65C16.775 4.5 16.1 4.5 15.575 4.5C13.625 4.5 11.75 4.5 9.8 4.5C8.975 4.5 8.075 4.5 7.4 4.875C6.725 5.25 6.2 6 5.825 6.525C5.675 6.75 5.6 6.9 5.45 6.9C5.3 7.05 5.075 7.05 4.925 7.05C4.1 7.05 3.5 7.425 3.05 7.95C2.75 8.325 2.75 9 3.05 9.45C3.2 9.675 3.2 10.05 2.975 10.275C2.6 10.725 2.375 11.175 2.225 11.7C2.075 12.45 2.075 13.2 2 13.95V14.025C2 15.15 2 16.275 2.15 17.4C2.3 18.3 2.525 19.2 3.575 19.425C4.475 19.65 5.525 19.575 6.35 18.9C6.5 18.75 6.5 18.75 6.65 18.6C6.8 18.6 6.95 18.6 7.025 18.6C10.7 18.6 14.45 18.6 18.125 18.6C18.275 18.6 18.275 18.6 18.425 18.6C18.575 18.6 18.575 18.75 18.725 18.825C19.025 19.2 19.55 19.2 20.075 19.35C20.75 19.5 21.425 19.5 22.025 19.125C22.7 18.75 22.85 18 22.925 17.25C23.075 16.35 23.075 15.45 23.075 14.625C23.075 14.4 23.075 14.1 23.075 13.875C23 13.05 23 12.3 22.7 11.55ZM21.875 14.55C21.875 15.45 21.875 16.2 21.725 17.025C21.65 17.475 21.575 18 21.35 18.075C21.125 18.225 20.9 18.225 20.675 18.225C20.525 18.225 20.375 18.225 20.15 18.15C20.075 18.15 19.925 18.075 19.775 18.075C19.7 18.075 19.55 18 19.475 18C19.1 17.475 18.725 17.4 18.425 17.4H6.2L5.6 18C5.225 18.3 4.775 18.375 4.475 18.375C4.25 18.375 4.1 18.375 3.875 18.3C3.65 18.225 3.35 18.15 3.275 17.175C3.125 16.2 3.125 15.15 3.125 14.025C3.2 13.65 3.2 13.275 3.275 12.9C3.275 12.525 3.35 12.225 3.425 11.85C3.5 11.4 3.725 11.025 4.175 10.65L4.7 10.2C5.075 9.9 5.075 9.3 4.625 9.075L4.025 8.7C3.95 8.7 3.95 8.475 3.95 8.475C4.175 8.25 4.475 8.1 4.925 8.1C5.075 8.1 5.525 8.1 5.9 7.875C6.2 7.8 6.5 7.575 6.8 7.05L6.875 6.9C7.175 6.45 7.625 6 8 5.775C8.375 5.55 9.2 5.55 9.8 5.55H15.575C16.025 5.55 16.475 5.55 16.775 5.7C17.225 5.925 17.6 6.375 17.975 6.9L18.275 7.275L18.35 7.35C18.5 7.575 18.725 7.875 19.175 8.025L19.4 8.1H20.075C20.45 8.1 20.825 8.25 20.975 8.475V8.55V8.625C20.975 8.625 20.975 8.7 20.9 8.7L20.375 9C19.925 9.225 19.925 9.825 20.3 10.125L20.825 10.575C21.275 10.875 21.5 11.325 21.575 11.775V11.85V11.925C21.8 12.525 21.8 13.125 21.8 13.8V14.55H21.875Z" fill="currentColor"/>
    </svg>
  );
};

export default CarIconV2; 