import React from 'react';

interface ClockIconV2Props {
  className?: string;
}

const ClockIconV2: React.FC<ClockIconV2Props> = ({ className = "w-6 h-6" }) => {
  return (
    <svg 
      width="25" 
      height="24" 
      viewBox="0 0 25 24" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path d="M10.9899 2.23006L5.99991 4.10006C4.84991 4.53006 3.90991 5.89006 3.90991 7.12006V14.5501C3.90991 15.7301 4.68992 17.2801 5.63992 17.9901L9.93991 21.2001C11.3499 22.2601 13.6699 22.2601 15.0799 21.2001L19.3799 17.9901C20.3299 17.2801 21.1099 15.7301 21.1099 14.5501V7.12006C21.1099 5.89006 20.1699 4.53006 19.0199 4.10006L14.0299 2.23006C13.1799 1.92006 11.8199 1.92006 10.9899 2.23006Z" stroke="currentColor" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M12.5 15.5C14.7091 15.5 16.5 13.7091 16.5 11.5C16.5 9.29086 14.7091 7.5 12.5 7.5C10.2909 7.5 8.5 9.29086 8.5 11.5C8.5 13.7091 10.2909 15.5 12.5 15.5Z" stroke="currentColor" strokeWidth="1.3" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M12.75 10.25V11.18C12.75 11.53 12.57 11.86 12.26 12.04L11.5 12.5" stroke="currentColor" strokeWidth="1.3" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );
};

export default ClockIconV2; 