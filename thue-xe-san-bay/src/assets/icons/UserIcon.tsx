import React from 'react';

interface UserIconProps {
  className?: string;
}

const UserIcon: React.FC<UserIconProps> = ({ className = "w-5 h-5" }) => {
  return (
    <svg 
      width="21" 
      height="20" 
      viewBox="0 0 21 20" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path d="M8.13314 9.05835C8.0498 9.05002 7.9498 9.05002 7.85814 9.05835C5.8748 8.99169 4.2998 7.36669 4.2998 5.36669C4.2998 3.32502 5.9498 1.66669 7.9998 1.66669C10.0415 1.66669 11.6998 3.32502 11.6998 5.36669C11.6915 7.36669 10.1165 8.99169 8.13314 9.05835Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M14.1747 3.33337C15.7914 3.33337 17.0914 4.64171 17.0914 6.25004C17.0914 7.82504 15.8414 9.10837 14.2831 9.16671C14.2164 9.15837 14.1414 9.15837 14.0664 9.16671" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M3.9666 12.1334C1.94993 13.4834 1.94993 15.6834 3.9666 17.025C6.25827 18.5584 10.0166 18.5584 12.3083 17.025C14.3249 15.675 14.3249 13.475 12.3083 12.1334C10.0249 10.6084 6.2666 10.6084 3.9666 12.1334Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M15.7832 16.6667C16.3832 16.5417 16.9499 16.3 17.4165 15.9417C18.7165 14.9667 18.7165 13.3584 17.4165 12.3834C16.9582 12.0334 16.3999 11.8 15.8082 11.6667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );
};

export default UserIcon;
