import React from 'react';

interface CarIconProps {
  className?: string;
}

const CarIcon: React.FC<CarIconProps> = ({ className = "w-5 h-5" }) => {
  return (
    <svg 
      width="21" 
      height="20" 
      viewBox="0 0 21 20" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path fillRule="evenodd" clipRule="evenodd" d="M18.833 9.625C18.7705 9.1875 18.583 8.75 18.2705 8.4375C18.083 8.25 18.083 8 18.208 7.75C18.458 7.375 18.458 6.875 18.208 6.5625C17.8955 6.0625 17.208 5.8125 16.6455 5.8125C16.5205 5.8125 16.3955 5.8125 16.333 5.8125C16.083 5.6875 16.0205 5.5 15.8955 5.375C15.458 4.875 15.0205 4.25 14.333 3.875C13.8955 3.75 13.333 3.75 12.8955 3.75C11.2705 3.75 9.70801 3.75 8.08301 3.75C7.39551 3.75 6.64551 3.75 6.08301 4.0625C5.52051 4.375 5.08301 5 4.77051 5.4375C4.64551 5.625 4.58301 5.75 4.45801 5.75C4.33301 5.875 4.14551 5.875 4.02051 5.875C3.33301 5.875 2.83301 6.1875 2.45801 6.625C2.20801 6.9375 2.20801 7.5 2.45801 7.875C2.58301 8.0625 2.58301 8.375 2.39551 8.5625C2.08301 8.9375 1.89551 9.3125 1.77051 9.75C1.64551 10.375 1.64551 11 1.58301 11.625V11.6875C1.58301 12.625 1.58301 13.5625 1.70801 14.5C1.83301 15.25 2.02051 16 2.89551 16.1875C3.64551 16.375 4.52051 16.3125 5.20801 15.75C5.33301 15.625 5.33301 15.625 5.45801 15.5C5.58301 15.5 5.70801 15.5 5.77051 15.5C8.83301 15.5 11.958 15.5 15.0205 15.5C15.1455 15.5 15.1455 15.5 15.2705 15.5C15.3955 15.5 15.3955 15.625 15.5205 15.6875C15.7705 16 16.208 16 16.6455 16.125C17.208 16.25 17.7705 16.25 18.2705 15.9375C18.833 15.625 18.958 15 19.0205 14.375C19.1455 13.625 19.1455 12.875 19.1455 12.1875C19.1455 12 19.1455 11.75 19.1455 11.5625C19.083 10.875 19.083 10.25 18.833 9.625ZM18.1455 12.125C18.1455 12.875 18.1455 13.5 18.0205 14.1875C17.958 14.5625 17.8955 15 17.708 15.0625C17.5205 15.1875 17.333 15.1875 17.1455 15.1875C17.0205 15.1875 16.8955 15.1875 16.708 15.125C16.6455 15.125 16.5205 15.0625 16.3955 15.0625C16.333 15.0625 16.208 15 16.1455 15C15.833 14.5625 15.5205 14.5 15.2705 14.5H5.08301L4.58301 15C4.27051 15.25 3.89551 15.3125 3.64551 15.3125C3.45801 15.3125 3.33301 15.3125 3.14551 15.25C2.95801 15.1875 2.70801 15.125 2.64551 14.3125C2.52051 13.5 2.52051 12.625 2.52051 11.6875C2.58301 11.375 2.58301 11.0625 2.64551 10.75C2.64551 10.4375 2.70801 10.1875 2.77051 9.875C2.83301 9.5 3.02051 9.1875 3.39551 8.875L3.83301 8.5C4.14551 8.25 4.14551 7.75 3.77051 7.5625L3.27051 7.25C3.20801 7.25 3.20801 7.0625 3.20801 7.0625C3.39551 6.875 3.64551 6.75 4.02051 6.75C4.14551 6.75 4.52051 6.75 4.83301 6.5625C5.08301 6.5 5.33301 6.3125 5.58301 5.875L5.64551 5.75C5.89551 5.375 6.27051 5 6.58301 4.8125C6.89551 4.625 7.58301 4.625 8.08301 4.625H12.8955C13.2705 4.625 13.6455 4.625 13.8955 4.75C14.2705 4.9375 14.583 5.3125 14.8955 5.75L15.1455 6.0625L15.208 6.125C15.333 6.3125 15.5205 6.5625 15.8955 6.6875L16.083 6.75H16.6455C16.958 6.75 17.2705 6.875 17.3955 7.0625V7.125V7.1875C17.3955 7.1875 17.3955 7.25 17.333 7.25L16.8955 7.5C16.5205 7.6875 16.5205 8.1875 16.833 8.4375L17.2705 8.8125C17.6455 9.0625 17.833 9.4375 17.8955 9.8125V9.875V9.9375C18.083 10.4375 18.083 10.9375 18.083 11.5V12.125H18.1455Z" fill="currentColor"/>
      <path d="M13.0834 7.31251C13.0209 7.25001 13.0209 7.31251 12.9584 7.31251C12.8334 7.37501 12.7084 7.43751 12.5834 7.50001C12.4584 7.56251 12.3959 7.62501 12.3334 7.68751C11.9584 8.25001 11.5209 8.81251 11.1459 9.31251C10.9584 9.56251 10.7709 9.87501 10.5209 10.125C10.2709 10.375 10.2709 10.375 10.0834 10.125C9.58344 9.56251 9.14594 8.87501 8.70844 8.25001C8.45844 7.87501 8.20844 7.43751 7.70844 7.37501C7.64594 7.31251 7.64594 7.31251 7.58344 7.31251C7.52094 7.37501 7.58344 7.43751 7.58344 7.50001C7.83344 8.06251 8.08344 8.68751 8.33344 9.25001C8.70844 10.125 9.08344 11 9.58344 11.875C9.70844 12.125 9.89594 12.375 10.0834 12.5625C10.2709 12.75 10.3959 12.75 10.5834 12.5625C10.7084 12.4375 10.7709 12.3125 10.8959 12.1875C11.3334 11.5 11.7084 10.75 12.0834 10C12.4584 9.18751 12.8334 8.31251 13.2084 7.50001C13.1459 7.43751 13.1459 7.37501 13.0834 7.31251ZM12.3334 8.62501C11.8959 9.43751 11.4584 10.3125 11.0209 11.125C10.8334 11.4375 10.6459 11.75 10.4584 12.0625C10.3959 12.1875 10.3334 12.125 10.2084 12.0625C10.0834 11.9375 10.0209 11.8125 9.95844 11.6875C9.58344 11.0625 9.27094 10.4375 8.95844 9.81251C8.58344 9.06251 8.20844 8.25001 7.70844 7.50001V7.43751C7.70844 7.56251 7.83344 7.56251 7.89594 7.62501C8.08344 7.68751 8.20844 7.81251 8.33344 7.93751C8.70844 8.50001 9.08344 9.00001 9.39594 9.56251C9.58344 9.81251 9.77094 10.0625 9.95844 10.3125C10.2709 10.625 10.3334 10.625 10.6459 10.3125C11.2084 9.62501 11.6459 8.93751 12.1459 8.18751C12.2084 8.06251 12.3334 7.93751 12.3959 7.81251H12.4584C12.5834 7.68751 12.7709 7.62501 12.9584 7.56251C12.8959 7.62501 12.8959 7.68751 12.8334 7.75001C12.6459 8.00001 12.4584 8.31251 12.3334 8.62501Z" fill="currentColor"/>
      <path d="M12.458 7.875C12.083 8.5 11.6455 9.125 11.2705 9.75C11.0205 10.125 10.833 10.5 10.5205 10.875C10.3955 11 10.333 11 10.208 10.875C9.89551 10.5625 9.70801 10.1875 9.45801 9.8125C9.08301 9.125 8.64551 8.5 8.27051 7.8125C8.20801 7.6875 8.14551 7.625 8.02051 7.5625C8.02051 7.625 8.02051 7.6875 8.08301 7.6875C8.39551 8.1875 8.64551 8.75 8.95801 9.25C9.33301 9.875 9.64551 10.5 10.083 11.125C10.333 11.5 10.3955 11.5 10.708 11.125C11.1455 10.5625 11.3955 9.9375 11.7705 9.375C12.0205 9.0625 12.1455 8.6875 12.333 8.3125C12.458 8.0625 12.583 7.8125 12.7705 7.5625C12.583 7.6875 12.5205 7.8125 12.458 7.875Z" fill="currentColor"/>
      <path fillRule="evenodd" clipRule="evenodd" d="M8.70801 12.6875C8.70801 12.875 8.58301 13 8.39551 12.8125C8.27051 12.6875 8.27051 12.625 8.20801 12.5625C8.08301 12.25 8.02051 12 7.70801 11.8125C7.39551 11.5625 6.89551 11.5625 6.52051 11.4375C6.20801 11.25 5.95801 11.125 5.64551 11.0625C5.52051 11 5.33301 11 5.27051 11C5.14551 11 5.08301 11.0625 4.95801 11.0625C4.77051 11.1875 4.45801 11.0625 4.27051 11C3.95801 10.875 3.77051 10.6875 3.58301 10.4375C3.45801 10.3125 3.39551 10.125 3.27051 9.93752C3.27051 9.75002 3.39551 9.56252 3.45801 9.43752C3.64551 9.31252 3.95801 9.43752 4.14551 9.62502C5.14551 10.1875 6.20801 10.3125 7.33301 10.5625C7.64551 10.6875 7.95801 10.75 8.14551 10.9375C8.33301 11.125 8.45801 11.3125 8.45801 11.5C8.58301 11.8125 8.64551 12.0625 8.64551 12.4375C8.77051 12.5 8.77051 12.5625 8.70801 12.6875Z" fill="currentColor"/>
      <path fillRule="evenodd" clipRule="evenodd" d="M12.0832 12.6875C12.0832 12.875 12.2082 13 12.3957 12.8125C12.5207 12.6875 12.5207 12.625 12.5832 12.5625C12.7082 12.25 12.7707 12 13.0832 11.8125C13.3957 11.5625 13.8957 11.5625 14.2707 11.4375C14.5832 11.3125 14.8332 11.25 15.1457 11.125C15.2707 11.0625 15.4582 11.0625 15.5207 11.0625C15.6457 11.0625 15.7082 11.125 15.8332 11.125C16.0207 11.25 16.3332 11.125 16.5207 11.0625C16.8332 10.9375 17.0207 10.75 17.2082 10.5C17.3332 10.375 17.3957 10.1875 17.5207 10C17.5207 9.81252 17.3957 9.62502 17.3332 9.50002C17.1457 9.37502 16.8332 9.50002 16.6457 9.68752C15.6457 10.25 14.5832 10.375 13.4582 10.625C13.1457 10.75 12.8332 10.8125 12.6457 11C12.4582 11.1875 12.3332 11.375 12.3332 11.5625C12.2082 11.875 12.1457 12.125 12.1457 12.5C11.9582 12.5 11.9582 12.5625 12.0832 12.6875Z" fill="currentColor"/>
    </svg>
  );
};

export default CarIcon;
