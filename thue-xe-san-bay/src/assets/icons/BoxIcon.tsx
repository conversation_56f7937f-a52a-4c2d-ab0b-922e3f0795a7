import React from 'react';

interface BoxIconProps {
  className?: string;
}

const BoxIcon: React.FC<BoxIconProps> = ({ className = "w-5 h-5" }) => {
  return (
    <svg 
      width="21" 
      height="20" 
      viewBox="0 0 21 20" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path d="M2.80762 6.20001L10.1659 10.4583L17.4743 6.22499" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M10.166 18.0084V10.45" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M8.44054 2.06669L3.99054 4.54171C2.98221 5.10005 2.15723 6.50003 2.15723 7.65003V12.3584C2.15723 13.5084 2.98221 14.9084 3.99054 15.4667L8.44054 17.9417C9.39054 18.4667 10.9489 18.4667 11.8989 17.9417L16.3489 15.4667C17.3572 14.9084 18.1822 13.5084 18.1822 12.3584V7.65003C18.1822 6.50003 17.3572 5.10005 16.3489 4.54171L11.8989 2.06669C10.9405 1.53335 9.39054 1.53335 8.44054 2.06669Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M14.3322 11.0334V7.98339L6.42383 3.41669" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );
};

export default BoxIcon;
