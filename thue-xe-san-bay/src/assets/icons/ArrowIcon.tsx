import React from "react";

interface ArrowIconProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

const ArrowIcon: React.FC<ArrowIconProps> = ({
  width = 24,
  height = 24,
  color = "black",
  className = "",
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M5 12H19M19 12L12 5M19 12L12 19"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default ArrowIcon; 