import React from "react";

interface TimerIconProps {
  className?: string;
}

const TimerIcon: React.FC<TimerIconProps> = ({
  className = "w-5 h-5 ml-2 opacity-50",
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      className={className}
    >
      <path
        d="M17.2923 11.0417C17.2923 15.0667 14.0257 18.3333 10.0007 18.3333C5.97565 18.3333 2.70898 15.0667 2.70898 11.0417C2.70898 7.01667 5.97565 3.75 10.0007 3.75C14.0257 3.75 17.2923 7.01667 17.2923 11.0417Z"
        stroke="#090806"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M10 6.66602V10.8327"
        stroke="#090806"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M7.5 1.66699H12.5"
        stroke="#090806"
        stroke-width="1.5"
        stroke-miterlimit="10"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export default TimerIcon;
