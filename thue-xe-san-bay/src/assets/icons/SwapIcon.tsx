import React from 'react';

interface SwapIconProps {
  className?: string;
}

const SwapIcon: React.FC<SwapIconProps> = ({ className = "text-gray-600" }) => {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      width="24" 
      height="24" 
      viewBox="0 0 24 24" 
      fill="none" 
      stroke="currentColor" 
      strokeWidth="2" 
      strokeLinecap="round" 
      strokeLinejoin="round" 
      className={className}
    >
      <path d="M7 10h10l-3-3" />
      <path d="M17 14H7l3 3" />
    </svg>
  );
};

export default SwapIcon; 