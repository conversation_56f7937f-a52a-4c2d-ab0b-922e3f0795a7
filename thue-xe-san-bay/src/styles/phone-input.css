/* Tùy chỉnh giao diện PhoneInput */

/* Container ch<PERSON>h */
.react-international-phone-input-container {
  width: 100%;
  display: flex !important;
  align-items: center !important;
}

/* Dropdown quốc gia */
.react-international-phone-country-selector-dropdown {
  border-radius: 0.375rem;
  border-color: #e5e7eb;
  z-index: 50;
}

/* Button chọn quốc gia */
.react-international-phone-country-selector-button {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
  border-color: #e5e7eb;
  background-color: #f9fafb;
  height: 3rem !important;
  display: flex !important;
  align-items: center !important;
  padding: 0 0.5rem !important;
}

/* Input */
.react-international-phone-input {
  height: 3rem !important;
  border-top-right-radius: 0.375rem !important;
  border-bottom-right-radius: 0.375rem !important;
  padding-left: 0.75rem !important;
  padding-right: 0.75rem !important;
  flex: 1 !important;
}

/* Focus styles */
.react-international-phone-input:focus {
  outline: none !important;
  box-shadow: 0 0 0 2px rgba(7, 246, 104, 0.5) !important;
  border-color: #07F668 !important;
}

/* <PERSON>hi input được focus, cũng áp dụng style cho button */
.react-international-phone-input:focus ~ .react-international-phone-country-selector-button {
  border-color: #07F668 !important;
  box-shadow: 0 0 0 2px rgba(7, 246, 104, 0.5) !important;
}

/* Khi container được focus-within, áp dụng style cho cả input và button */
.phone-input-wrapper:focus-within .react-international-phone-country-selector-button,
.phone-input-wrapper:focus-within .react-international-phone-input {
  border-color: #07F668 !important;
  box-shadow: 0 0 0 2px rgba(7, 246, 104, 0.5) !important;
}

/* Error styles */
.error .react-international-phone-input,
.error .react-international-phone-country-selector-button {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 1px #ef4444 !important;
}

/* Đảm bảo mã quốc gia hiển thị đúng */
.react-international-phone-country-selector-button .react-international-phone-country-selector-button-flag {
  margin-right: 8px !important;
}

.react-international-phone-country-selector-button .react-international-phone-country-selector-button-dial-code {
  display: inline-block !important;
  margin-right: 4px !important;
  color: #6b7280 !important;
  font-size: 0.875rem !important;
}

/* Đảm bảo mã quốc gia hiển thị đúng trong dropdown */
.react-international-phone-country-selector-dropdown-item-dial-code {
  color: #6b7280 !important;
}

/* Đảm bảo dropdown hiển thị đúng */
.react-international-phone-country-selector-dropdown-container {
  z-index: 100 !important;
}

/* Đảm bảo container hiển thị đúng */
.react-international-phone-input-container {
  display: flex !important;
  width: 100% !important;
}

/* Wrapper cho phone input */
.phone-input-wrapper {
  width: 100%;
}

/* Đảm bảo input và button nằm trên cùng một dòng */
.phone-input-wrapper .react-international-phone-input-container {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
}

/* Wrapper cho focus */
.phone-input-focus-wrapper {
  position: relative;
}