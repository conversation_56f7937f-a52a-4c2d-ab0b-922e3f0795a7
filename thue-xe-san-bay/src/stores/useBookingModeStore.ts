import { AirportPricingResponse } from "@/api/services/location";
import { create } from "zustand";
import { devtools } from "zustand/middleware";

type BookingMode = "journey" | "hourly";

interface BookingModeState {
  // State
  mode: BookingMode;

  // Actions
  setMode: (mode: BookingMode) => void;

  pricingList: AirportPricingResponse[];

  // Actions
  setPricingList: (list: AirportPricingResponse[]) => void;
  clearPricingList: () => void;
}

export const useBookingModeStore = create<BookingModeState>()(
  devtools((set) => ({
    // Initial state
    mode: "journey", // Default to journey mode

    // Actions
    setMode: (mode) => set({ mode }),
    pricingList: [],

    setPricingList: (list) => set({ pricingList: list }),
    clearPricingList: () => set({ pricingList: [] }),
  }))
);
