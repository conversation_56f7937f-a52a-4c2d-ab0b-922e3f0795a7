import { create } from 'zustand';
import { Holiday, holidayService } from '../api/services/holiday';
import { format } from 'date-fns';
import i18n from '../i18n';

interface HolidayStore {
  holidays: Holiday[];
  isLoading: boolean;
  currentLanguage: string;
  setHolidays: (holidays: Holiday[]) => void;
  isHolidayDate: (date: Date) => Holiday | undefined;
  fetchHolidays: () => Promise<void>;
  refetchOnLanguageChange: () => void;
}

export const useHolidayStore = create<HolidayStore>((set, get) => ({
  holidays: [],
  isLoading: false,
  currentLanguage: i18n.language,
  setHolidays: (holidays) => set({ holidays }),
  isHolidayDate: (date) => {
    const formattedDate = format(date, 'yyyy-MM-dd');
    return get().holidays.find(holiday => holiday.holidayDate === formattedDate);
  },
  fetchHolidays: async () => {
    // Nếu đang loading, không gọi API
    if (get().isLoading) {
      return;
    }

    // Nếu đã có dữ liệu và ngôn ngữ không thay đổi, không gọi API
    if (get().holidays.length > 0 && get().currentLanguage === i18n.language) {
      return;
    }

    try {
      set({ isLoading: true });
      const data = i18n.language === 'en'
        ? await holidayService.getHolidayPricingUSD()
        : await holidayService.getHolidayPricing();
      set({
        holidays: data,
        currentLanguage: i18n.language
      });
    } catch (error) {
      console.error('Failed to fetch holidays:', error);
    } finally {
      set({ isLoading: false });
    }
  },
  refetchOnLanguageChange: () => {
    const currentLang = i18n.language;
    if (currentLang !== get().currentLanguage) {
      get().fetchHolidays();
    }
  },
}));

// Theo dõi thay đổi ngôn ngữ
i18n.on('languageChanged', () => {
  useHolidayStore.getState().refetchOnLanguageChange();
});
