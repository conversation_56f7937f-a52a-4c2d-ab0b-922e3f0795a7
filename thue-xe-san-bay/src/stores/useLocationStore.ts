import { LocationItem } from "@/components/search/LocationInput";
import { create } from "zustand";
import { devtools } from "zustand/middleware";

// Define the location point interface

// Define the location store state interface
interface LocationState {
  // State
  origin: LocationItem | null;
  destination: LocationItem | null;
  dateTime: string;

  // Actions
  setOrigin: (origin: LocationItem) => void;
  setDestination: (destination: LocationItem) => void;
  setDateTime: (dateTime: string) => void;
  swapLocations: () => void;
  resetLocations: () => void;
}

// Create the location store
export const useLocationStore = create<LocationState>()(
  devtools((set, get) => ({
    // Initial state
    origin: null,
    destination: null,
    // Actions
    setOrigin: (origin) => set({ origin }),

    setDestination: (destination) => set({ destination }),

    swapLocations: () => {
      const { origin, destination } = get();
      set({
        origin: destination,
        destination: origin,
      });
    },

    resetLocations: () =>
      set({
        origin: null,
        destination: null,
      }),
  }))
);
