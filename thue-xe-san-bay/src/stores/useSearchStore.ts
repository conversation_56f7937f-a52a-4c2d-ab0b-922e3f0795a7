/* eslint-disable @typescript-eslint/no-explicit-any */
import { create } from "zustand";
import { devtools } from "zustand/middleware";
import {
  type SearchFormValues,
  searchFormDefaultValues,
} from "../lib/validations";
import { locationService } from "../api";
import { LocationItem } from "@/components/search/LocationInput";
import { hasTodayAvailableTime, getFirstAvailableTime } from "../utils/time-utils";

// Định nghĩa kiểu dữ liệu cho airport options
export type AirportOption = {
  placeId: string;
  code: string;
  name: string;
  address: string;
  longitude: string;
  latitude: string;
  id: number;
};

export const isAirportName = (
  name: string,
  options: AirportOption[]
) => options.some((option) => option.name === name);

interface SearchStore extends SearchFormValues {
  // State
  pickUpLocation: LocationItem;
  destinationLocation: LocationItem;
  airportSelectedIn: "pickup" | "destination" | null;
  nonAirportSelectedIn: "pickup" | "destination" | null;
  airportOptions: AirportOption[];
  isLoadingAirports: boolean;
  // Actions
  setPickup: (pickup: LocationItem | undefined) => void;
  setDestination: (destination: LocationItem | undefined) => void;
  setDate: (date: Date | undefined) => void;
  setTime: (time: string) => void;
  swapLocations: () => void;
  resetForm: () => void;
  fetchAirportOptions: () => Promise<void>;
  // Derived data
  availablePickupOptions: () => AirportOption[];
  availableDestinationOptions: () => AirportOption[];
  shouldShowOnlyAirports: (isPickup: boolean) => boolean;
}

export const useSearchStore = create<SearchStore>()(
  devtools((set, get) => ({
    // Initial state
    ...searchFormDefaultValues,
    pickup: searchFormDefaultValues.pickup || "",
    destination: searchFormDefaultValues.destination || "",
    pickUpLocation: {} as LocationItem,
    destinationLocation: {} as LocationItem,
    airportSelectedIn: null,
    nonAirportSelectedIn: null,
    airportOptions: [],
    isLoadingAirports: false,

    date: (() => {
      // Kiểm tra xem ngày hôm nay còn giờ hợp lệ không
      const hasTimeToday = hasTodayAvailableTime();

      if (hasTimeToday) {
        // Nếu còn giờ hợp lệ, chọn ngày hôm nay
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return today;
      } else {
        // Nếu không còn giờ hợp lệ, chọn ngày mai
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(0, 0, 0, 0);
        return tomorrow;
      }
    })(),
    time: (() => {
      // Lấy ngày mặc định đã được tính toán ở trên
      const defaultDate = searchFormDefaultValues.date || null;
      // Lấy giờ hợp lệ đầu tiên dựa vào ngày đã chọn
      const defaultTime = getFirstAvailableTime(defaultDate);
      return defaultTime;
    })(),

    // Actions
    setPickup: (pickup) => {
      const isAirport =
        pickup && get().airportOptions.some((opt) => opt.name === pickup.name);
      set({
        pickup: pickup ? pickup.address : "",
        pickUpLocation: pickup || ({} as LocationItem),
        airportSelectedIn: isAirport
          ? "pickup"
          : get().airportSelectedIn === "pickup"
          ? null
          : get().airportSelectedIn,
        nonAirportSelectedIn:
          pickup && !isAirport
            ? "pickup"
            : get().nonAirportSelectedIn === "pickup"
            ? null
            : get().nonAirportSelectedIn,
      });
    },

    setDestination: (destination) => {
      const isAirport =
        destination &&
        get().airportOptions.some((opt) => opt.name === destination.name);
      set({
        destination: destination ? destination.address : "",
        destinationLocation: destination || ({} as LocationItem),
        airportSelectedIn: isAirport
          ? "destination"
          : get().airportSelectedIn === "destination"
          ? null
          : get().airportSelectedIn,
        nonAirportSelectedIn:
          destination && !isAirport
            ? "destination"
            : get().nonAirportSelectedIn === "destination"
            ? null
            : get().nonAirportSelectedIn,
      });
    },

    fetchAirportOptions: async () => {
      try {
        set({ isLoadingAirports: true });
        const response = await locationService.getAirportLocations();
        if (response.data?.content && response.data.content.length > 0) {
          const formattedOptions = response.data.content.map((airport) => ({
            placeId: airport.code,
            code: airport.code,
            name: airport.name,
            address: airport.address || airport.name,
            longitude: airport.longitude === null ? "" : (airport.longitude ? String(airport.longitude) : ""),
            latitude: airport.latitude === null ? "" : (airport.latitude ? String(airport.latitude) : ""),
            id: airport.id
          }));
          set({ airportOptions: formattedOptions });
        }
      } catch (error) {
        console.error("Error fetching airport options:", error);
      } finally {
        set({ isLoadingAirports: false });
      }
    },

    setDate: (date) => set({ date }),
    setTime: (time) => set({ time }),

    swapLocations: () => {
      const {
        pickUpLocation,
        destinationLocation,
        airportSelectedIn,
        nonAirportSelectedIn,
      } = get();

      // Tính toán giá trị pickup và destination dựa trên địa chỉ
      const newPickup = destinationLocation?.address || "";
      const newDestination = pickUpLocation?.address || "";

      set({
        pickup: newPickup,
        destination: newDestination,
        pickUpLocation: destinationLocation,
        destinationLocation: pickUpLocation,
        airportSelectedIn:
          airportSelectedIn === "pickup"
            ? "destination"
            : airportSelectedIn === "destination"
            ? "pickup"
            : null,
        nonAirportSelectedIn:
          nonAirportSelectedIn === "pickup"
            ? "destination"
            : nonAirportSelectedIn === "destination"
            ? "pickup"
            : null,
      });
    },

    resetForm: () =>
      set({
        ...searchFormDefaultValues,
        pickup: "",
        destination: "",
        pickUpLocation: {} as LocationItem,
        destinationLocation: {} as LocationItem,
        airportSelectedIn: null,
        nonAirportSelectedIn: null,
        date: (() => {
          // Kiểm tra xem ngày hôm nay còn giờ hợp lệ không
          const hasTimeToday = hasTodayAvailableTime();

          if (hasTimeToday) {
            // Nếu còn giờ hợp lệ, chọn ngày hôm nay
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            return today;
          } else {
            // Nếu không còn giờ hợp lệ, chọn ngày mai
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            tomorrow.setHours(0, 0, 0, 0);
            return tomorrow;
          }
        })(),
        time: (() => {
          // Lấy ngày mặc định đã được tính toán ở trên
          const defaultDate = searchFormDefaultValues.date || null;
          // Lấy giờ hợp lệ đầu tiên dựa vào ngày đã chọn
          const defaultTime = getFirstAvailableTime(defaultDate);
          return defaultTime;
        })(),
      }),

    // Derived data
    availablePickupOptions: () => {
      const { airportSelectedIn, airportOptions } = get();
      return airportSelectedIn === "destination" ? [] : airportOptions;
    },

    availableDestinationOptions: () => {
      const { airportSelectedIn, airportOptions } = get();
      return airportSelectedIn === "pickup" ? [] : airportOptions;
    },

    shouldShowOnlyAirports: (isPickup: boolean) => {
      const { nonAirportSelectedIn } = get();
      return nonAirportSelectedIn === (isPickup ? "destination" : "pickup");
    },
  }))
);
