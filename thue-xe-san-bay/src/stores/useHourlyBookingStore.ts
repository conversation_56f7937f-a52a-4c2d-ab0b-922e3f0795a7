import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { useProductStore } from './useProductStore';
import i18n from '../i18n';

interface VehiclePrice {
  id: number;
  price: number;
  sku: string;
}

interface HourlyBookingState {
  // State
  duration: string; // "0.5", "1", "2", "4"
  durationKey: string; // "30M", "1H", "2H", "4H"
  selectedVehicleModel: string | null; // "VF8", "VF9"

  // Actions
  setDuration: (duration: string) => void;
  setSelectedVehicleModel: (model: string | null) => void;
  resetHourlyBooking: () => void;

  // Derived data
  getFormattedDuration: () => string;
  getVehiclePrices: () => { [model: string]: VehiclePrice } | null;
  getSelectedVehiclePrice: () => number | null;
  getFormattedPrice: (price: number, priceUSD?: number) => string;
}

export const useHourlyBookingStore = create<HourlyBookingState>()(
  devtools((set, get) => ({
    // Initial state
    duration: "2", // Default to 2 hours
    durationKey: "2H", // Default to 2H
    selectedVehicleModel: null,

    // Actions
    setDuration: (duration) => {
      // Map duration to durationKey
      let durationKey = "4H";
      switch (duration) {
        case "0.5":
          durationKey = "30M";
          break;
        case "1":
          durationKey = "1H";
          break;
        case "2":
          durationKey = "2H";
          break;
        case "4":
          durationKey = "4H";
          break;
      }

      set({ duration, durationKey });
    },

    setSelectedVehicleModel: (model) => set({ selectedVehicleModel: model }),

    resetHourlyBooking: () => set({
      duration: "2",
      durationKey: "2H",
      selectedVehicleModel: null
    }),

    // Derived data
    getFormattedDuration: () => {
      const { duration } = get();
      switch (duration) {
        case "0.5":
          return "30 minutes";
        case "1":
          return "1 hour";
        case "2":
          return "2 hours";
        case "4":
          return "4 hours";
        default:
          return "";
      }
    },

    getVehiclePrices: () => {
      const { durationKey } = get();
      const transformedVariants = useProductStore.getState().getTransformedVariants();

      if (!transformedVariants || !transformedVariants[durationKey]) {
        return null;
      }

      return transformedVariants[durationKey];
    },

    getSelectedVehiclePrice: () => {
      const { durationKey, selectedVehicleModel } = get();
      const transformedVariants = useProductStore.getState().getTransformedVariants();

      if (!transformedVariants || !transformedVariants[durationKey] || !selectedVehicleModel) {
        return null;
      }

      return transformedVariants[durationKey][selectedVehicleModel]?.price || null;
    },

    getFormattedPrice: (price, priceUSD?: number) => {
      // Sử dụng giá USD nếu ngôn ngữ là tiếng Anh và có giá USD
      if (i18n.language === 'en' && priceUSD !== undefined) {
        return '$' + new Intl.NumberFormat('en-US').format(priceUSD);
      }
      // Mặc định sử dụng giá VND
      return new Intl.NumberFormat('vi-VN').format(price) + 'đ';
    },
  }))
);
