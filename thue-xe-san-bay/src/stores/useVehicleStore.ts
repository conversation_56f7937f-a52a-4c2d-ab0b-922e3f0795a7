import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { LocationResponse } from "@/api/services/location";
import { useHolidayStore } from "./holiday.store";
import i18n from "../i18n";

export interface VehiclePrice {
  vehicleType: string;
  vnd: number;
  usd: number;
}

interface VehicleStore {
  // State
  selectedLocation: LocationResponse | null;
  selectedDate: Date | null;

  // Actions
  setSelectedLocation: (location: LocationResponse) => void;
  setSelectedDate: (date: Date) => void;

  // Derived data
  getSelectedLocation: () => LocationResponse;
  getAvailableVehicles: () => VehiclePrice[];
  getFormattedPrice: (vehicleType: string) => string;
  getPrice: (vehicleType: string) => number;
  calculateHolidayPrice: (basePrice: number, baseUsdPrice: number) => { finalVnd: number; finalUsd: number };
}

export const useVehicleStore = create<VehicleStore>()(
  devtools((set, get) => ({
    // Initial state
    selectedLocation: null,
    selectedDate: null,

    // Actions
    setSelectedLocation: (location) => {
      set({ selectedLocation: location });
    },

    setSelectedDate: (date) => {
      set({ selectedDate: date });
    },

    getSelectedLocation: () => {
      const { selectedLocation } = get();
      return selectedLocation;
    },

    // Derived data
    getAvailableVehicles: () => {
      const { selectedLocation } = get();

      if (!selectedLocation || !selectedLocation.prices) {
        return [];
      }

      return selectedLocation.prices;
    },

    calculateHolidayPrice: (basePrice: number, baseUsdPrice: number) => {
      const { selectedDate } = get();
      if (!selectedDate) return { finalVnd: basePrice, finalUsd: baseUsdPrice };

      const holiday = useHolidayStore.getState().isHolidayDate(selectedDate);
      if (!holiday) return { finalVnd: basePrice, finalUsd: baseUsdPrice };

      if (holiday.currency === 'USD') {
        if (holiday.type === 'FIXED') {
          const additionalUsd = holiday.amount;
          const additionalVnd = additionalUsd * 23500; // Giả sử tỷ giá 1 USD = 23,500 VND
          return {
            finalVnd: basePrice + additionalVnd,
            finalUsd: baseUsdPrice + additionalUsd
          };
        } else {
          // PERCENTAGE type
          const additionalUsd = (baseUsdPrice * holiday.amount) / 100;
          const additionalVnd = (basePrice * holiday.amount) / 100;
          return {
            finalVnd: basePrice + additionalVnd,
            finalUsd: baseUsdPrice + additionalUsd
          };
        }
      } else {
        // VND currency
        if (holiday.type === 'FIXED') {
          const additionalVnd = holiday.amount;
          const additionalUsd = Math.round(additionalVnd / 23500); // Convert to USD
          return {
            finalVnd: basePrice + additionalVnd,
            finalUsd: baseUsdPrice + additionalUsd
          };
        } else {
          // PERCENTAGE type
          const additionalVnd = (basePrice * holiday.amount) / 100;
          const additionalUsd = (baseUsdPrice * holiday.amount) / 100;
          return {
            finalVnd: basePrice + additionalVnd,
            finalUsd: baseUsdPrice + additionalUsd
          };
        }
      }
    },

    getFormattedPrice: (vehicleType) => {
      const { selectedLocation } = get();
      const currentLanguage = i18n.language;

      if (!selectedLocation || !selectedLocation.prices) {
        return "N/A";
      }

      const vehicle = selectedLocation.prices.find(
        (price) => price.vehicleType === vehicleType
      );

      if (!vehicle) {
        return "N/A";
      }

      // Tính giá sau khi áp dụng ngày lễ
      const { finalVnd, finalUsd } = get().calculateHolidayPrice(vehicle.vnd, vehicle.usd);

      // Nếu ngôn ngữ là tiếng Anh, hiển thị giá USD
      if (currentLanguage === "en") {
        return "VND" + new Intl.NumberFormat("vi-VN").format(finalVnd) + " (~$" + finalUsd + ")";
      }

      // Mặc định hiển thị giá VND với định dạng tiếng Việt
      return new Intl.NumberFormat("vi-VN").format(finalVnd) + "đ";
    },

    getPrice: (vehicleType: string) => {
      const { selectedLocation } = get();
      const currentLanguage = i18n.language;

      if (!selectedLocation || !selectedLocation.prices) {
        return "N/A";
      }

      const vehicle = selectedLocation.prices.find(
        (price) => price.vehicleType === vehicleType
      );

      if (!vehicle) {
        return "N/A";
      }

      const { finalVnd, finalUsd } = get().calculateHolidayPrice(vehicle.vnd, vehicle.usd);
      return currentLanguage === "en" ? finalUsd : finalVnd;
    },
  }))
);
