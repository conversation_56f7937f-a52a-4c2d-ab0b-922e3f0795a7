/* eslint-disable @typescript-eslint/no-explicit-any */
import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { productService } from "../api";

// Đ<PERSON><PERSON> nghĩa kiểu dữ liệu cho product variant
export interface ProductVariant {
  id: number;
  sku: string;
  name: string;
  price: number;
  serviceType: string;
  attributes?: {
    duration?: string;
    model?: string;
    rental_type?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

// Định nghĩa kiểu dữ liệu cho product variant đ<PERSON> được chuyển đổi
export interface TransformedProductVariants {
  [duration: string]: {
    [model: string]: {
      id: number;
      price: number;
      sku: string;
    };
  };
}

interface ProductStore {
  // State
  productVariants: ProductVariant[];
  transformedVariants: TransformedProductVariants | null;
  isLoading: boolean;
  error: string | null;
  lastFetchedServiceType: string | null;

  // Actions
  fetchProductVariants: (serviceType: string) => Promise<void>;
  getProductVariants: (serviceType: string) => Promise<ProductVariant[]>;
  getTransformedVariants: () => TransformedProductVariants | null;
  clearProductVariants: () => void;
  transformProductVariants: (variants: ProductVariant[]) => TransformedProductVariants;
}

export const useProductStore = create<ProductStore>()(
  devtools((set, get) => ({
    // Initial state
    productVariants: [],
    transformedVariants: null,
    isLoading: false,
    error: null,
    lastFetchedServiceType: null,

    // Hàm chuyển đổi dữ liệu product variants sang định dạng mới
    transformProductVariants: (variants: ProductVariant[]) => {
      // Tạo object rỗng để lưu kết quả
      const result: TransformedProductVariants = {};

      // Duyệt qua từng variant
      variants.forEach(variant => {
        // Lấy duration và model từ sku hoặc attributes
        let duration = '';
        let model = '';

        // Lấy từ sku (cách nhanh nhất)
        if (variant.sku) {
          const parts = variant.sku.split('_');
          if (parts.length >= 3) {
            model = parts[1]; // VF8 hoặc VF9
            duration = parts[2]; // 30M, 1H, 2H, 4H
          }
        }
        // Backup: lấy từ attributes nếu có
        else if (variant.attributes) {
          duration = variant.attributes.duration?.replace(' ', '') || '';
          model = variant.attributes.model || '';
        }

        // Nếu không có duration hoặc model, bỏ qua
        if (!duration || !model) return;

        // Chuẩn hóa duration (bỏ khoảng trắng, chuyển "Mins" thành "M", "Hour" thành "H")
        duration = duration.replace('Mins', 'M').replace('Hour', 'H').replace('Hours', 'H').replace(' ', '');

        // Tạo cấu trúc nếu chưa có
        if (!result[duration]) {
          result[duration] = {};
        }

        // Thêm thông tin variant vào kết quả
        result[duration][model] = {
          id: variant.id,
          price: variant.price,
          sku: variant.sku
        };
      });

      return result;
    },

    // Actions
    fetchProductVariants: async (serviceType: string) => {
      // Nếu đã có dữ liệu cho serviceType này, không cần gọi API lại
      if (get().lastFetchedServiceType === serviceType && get().productVariants.length > 0) {
        return;
      }

      set({ isLoading: true, error: null });

      try {
        const response = await productService.getProductVariants(serviceType);

        if (response.error) {
          set({
            error: response.error,
            isLoading: false
          });
          return;
        }

        // Lấy dữ liệu từ response
        const responseData = response.data as { content?: ProductVariant[] } || {};
        const variants = responseData.content || [];

        // Chuyển đổi dữ liệu sang định dạng mới
        const transformed = get().transformProductVariants(variants);

        // Lưu cả dữ liệu gốc và dữ liệu đã chuyển đổi
        set({
          productVariants: variants,
          transformedVariants: transformed,
          isLoading: false,
          lastFetchedServiceType: serviceType
        });
      } catch (error) {
        set({
          error: error instanceof Error ? error.message : "Unknown error",
          isLoading: false
        });
      }
    },

    getProductVariants: async (serviceType: string) => {
      // Nếu đang loading hoặc có lỗi, trả về mảng rỗng
      if (get().isLoading || get().error) {
        return [];
      }

      // Nếu chưa có dữ liệu hoặc serviceType khác với lần fetch trước, gọi API
      if (get().productVariants.length === 0 || get().lastFetchedServiceType !== serviceType) {
        await get().fetchProductVariants(serviceType);
      } else {
        console.warn(`useProductStore.getProductVariants: Using cached data for ${serviceType}`);
      }

      return get().productVariants;
    },

    getTransformedVariants: () => {
      // Nếu đã có dữ liệu đã chuyển đổi, trả về luôn
      if (get().transformedVariants) {
        return get().transformedVariants;
      }

      // Nếu có dữ liệu gốc nhưng chưa chuyển đổi, thực hiện chuyển đổi
      if (get().productVariants.length > 0) {
        const transformed = get().transformProductVariants(get().productVariants);
        set({ transformedVariants: transformed });
        return transformed;
      }

      return null;
    },

    clearProductVariants: () => {
      set({
        productVariants: [],
        transformedVariants: null,
        lastFetchedServiceType: null
      });
    },
  }))
);
