{"name": "thue-xe-san-bay", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc -b && vite build --base=/thue-xe-san-bay/", "build:serve": "tsc -b && vite build --base=/thue-xe-san-bay/", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@mantine/core": "^7.17.2", "@mantine/hooks": "^7.17.2", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@tailwindcss/vite": "^4.0.15", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.5.0", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.4", "libphonenumber-js": "^1.12.9", "lucide-react": "^0.483.0", "path": "^0.12.7", "query-string": "^9.1.1", "react": "^19.0.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-google-recaptcha-v3": "^1.10.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.54.2", "react-i18next": "^15.4.1", "react-international-phone": "^4.5.0", "react-scroll": "^1.9.3", "shadcn-ui": "^0.9.5", "tailwind-merge": "^3.0.2", "tw-animate-css": "^1.2.4", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/node": "^22.13.10", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-scroll": "^1.8.10", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "^8.5.3", "postcss-preset-mantine": "^1.17.0", "postcss-simple-vars": "^7.0.1", "tailwindcss": "^4.0.15", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}