import { defineConfig } from "vite";
import path from "path";
import react from "@vitejs/plugin-react";
import tailwindcss from "@tailwindcss/vite";
import fs from 'fs';

// https://vite.dev/config/
export default defineConfig({
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    },
  },
  plugins: [
    react(),
    tailwindcss(),
    {
      name: 'handle-html-i18n',
      // Tạo file HTML riêng cho phiên bản tiếng Anh khi build
      closeBundle() {
        if (process.env.NODE_ENV === 'production') {
          // Tạo thư mục /en nếu chưa tồn tại
          if (!fs.existsSync('./dist/en')) {
            fs.mkdirSync('./dist/en', { recursive: true });
          }
          // Copy index.html vào thư mục /en
          fs.copyFileSync('./dist/index.html', './dist/en/index.html');
        }
      }
    }
  ],
  // <PERSON><PERSON><PERSON> hình cho Vite để xử lý rewrite
  server: {
    proxy: {
      "/en": {
        target: "http://localhost:5173",
        rewrite: () => "/index.html"
      }
    }
  }
});
