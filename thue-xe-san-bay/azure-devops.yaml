trigger:
  branches:
    include:
      - development
      - staging
      - main
  paths:
    include:
      - thue-xe-san-bay/**

variables:
  - name: SERVICE
    value: ldp-txsb
  - name: ROOT_DIR
    value: thue-xe-san-bay
  - name: POOL_NAME
    ${{ if eq(variables['Build.SourceBranchName'], 'main') }}:
      value: FGF-platform-agent-prod
    ${{ else }}:
      value: FGF-platform-agent-dev
  - ${{ if eq(variables['Build.SourceBranchName'], 'development') }}:
      - group: AWS-Config-Dev
  - ${{ if eq(variables['Build.SourceBranchName'], 'staging') }}:
      - group: AWS-Config-Staging
  - ${{ if eq(variables['Build.SourceBranchName'], 'main') }}:
      - group: AWS-Config-Prod
  - ${{ if eq(variables['Build.SourceBranchName'], 'development') }}:
      - group: LDP-TXSB-Dev
  - ${{ if eq(variables['Build.SourceBranchName'], 'staging') }}:
      - group: LDP-TXSB-Staging
  - ${{ if eq(variables['Build.SourceBranchName'], 'main') }}:
      - group: LDP-TXSB-Prod

pool:
  name: $(POOL_NAME)

stages:
  - stage: Docker
    jobs:
      - job: Docker
        displayName: 'Docker'
        steps:
          - script: |
              aws ecr get-login-password --region $(AWS_REGION) | docker login --username AWS --password-stdin $(AWS_ACCOUNT_ID).dkr.ecr.$(AWS_REGION).amazonaws.com
            displayName: 'Login to AWS'
            env:
              AWS_ACCESS_KEY_ID: $(AWS_ACCESS_KEY_ID)
              AWS_SECRET_ACCESS_KEY: $(AWS_SECRET_ACCESS_KEY)

          - script: |
              set -e
              cd $(ROOT_DIR)
              # Create .env file
              cat > .env << EOF
              VITE_BASE_API_ENDPOINT=$(VITE_BASE_API_ENDPOINT)
              VITE_BASE_PATH=$(VITE_BASE_PATH)
              VITE_CAMPAIGN_CODE=$(VITE_CAMPAIGN_CODE)
              VITE_RECAPTCHA_SITE_KEY=$(VITE_RECAPTCHA_SITE_KEY)
              EOF
              # Pull latest image to use for cache
              docker pull $(AWS_ECR_URI_LDP_TXSB):latest || true

              docker build \
                --cpuset-cpus="0-1" \
                --memory="3g" \
                --build-arg BUILDKIT_INLINE_CACHE=1 \
                --cache-from $(AWS_ECR_URI_LDP_TXSB):latest \
                -t $(AWS_ECR_URI_LDP_TXSB):latest .

              docker push $(AWS_ECR_URI_LDP_TXSB):latest
            displayName: 'Build and Push Docker Image'
            env:
              AWS_ACCESS_KEY_ID: $(AWS_ACCESS_KEY_ID)
              AWS_SECRET_ACCESS_KEY: $(AWS_SECRET_ACCESS_KEY)

  - stage: Deploy
    displayName: Deploy
    dependsOn: Docker
    jobs:
      - job: Deploy
        displayName: Deploy
        steps:
          - script: |
              kubectl -n $(KUBE_NAMESPACE) rollout restart deployment $(SERVICE)